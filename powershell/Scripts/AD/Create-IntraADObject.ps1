<#
.SYNOPSIS
    Creates an AD Computer Account from the AutoDeploy service.

.DESCRIP<PERSON>ON
    Creates an AD Computer Account from the AutoDeploy service, for Windows, Clusters and VDI virtual machines.
    Prefers intra-site domain controllers for better replication performance, with automatic fallback to PDC Emulator if intra-site DCs are unreachable.

.PARAMETER jobId
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    List of cluster nodes for CLS/LST objects. Can be separated by comma (,), space ( ), or pipe (|). Supports both short names (NODE01) and FQDNs (NODE01.domain.com).

.PARAMETER siteName
    Optional. The deployment location to determine domain controller selection strategy. Defaults to "aws".
    - "aws": Creates objects on AWS domain controllers to avoid replication delays to cloud
    - "az": Creates objects on Azure domain controllers to avoid replication delays to cloud
    - "on-prem": Creates objects on PDC Emulator (on-premises), replication delays acceptable
    If specified domain controllers are unreachable, the script will automatically fallback to the PDC Emulator.

.EXAMPLE
    .\Create-IntraADObject.ps1 -jobId "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"
    .\Create-IntraADObject.ps1 -jobId "456" -objectName "CLS001" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01,NODE02"
    .\Create-IntraADObject.ps1 -jobId "789" -objectName "LST001" -objectDescription "List VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01 NODE02 NODE03" -siteName "aws"
    .\Create-IntraADObject.ps1 -jobId "101" -objectName "CLS002" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01|NODE02|NODE03" -siteName "az"
    .\Create-IntraADObject.ps1 -jobId "102" -objectName "CLS003" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01.example.com,NODE02.example.com" -siteName "on-prem"

.NOTES
    File Name   : Create-IntraADObject.ps1
    Author      : Rudi van Zyl

    Version 1.7 - Added intelligent fallback mechanism: intra-site DC -> PDC Emulator -> any available DC -> domain name.
    Version 1.6 - Forked and upated to use intra-site domain controllers, instead of PDC, to help with replication times.
    Version 1.5 - Added support for 'CLS' and 'LST' objects.
    Version 1.4 - Simplified further, removed unnecessary parameters and modules.
    Version 1.3 - Simplified and modernized the script.
    Version 1.2 - Refactored the script to be called instead of running on a schedule checking whether a job exists or not.
    Version 1.1 - Re-wrote Using AutoDeploy module
    Version 1.0.2 - Added issue_ref and issue_type to the API update.
    Version 1.0.1 - Added skipping AD object creation if this is a linux server.
    Version 1.0 - Base Script
#>
#Requires -Version 5
#Requires -Modules CredentialManager, ActiveDirectory

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$jobId,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectDescription,

    <#
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$vmOS,
    #>
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateScript({
    if ($_ -match "Windows|Linux") {
        $true
    } else {
        throw "OS must contain 'Windows' or 'Linux'. Received: $_"
    }
    })]
    [string]$vmOS,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ouPath,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$clusterNodes,

    [Parameter(Mandatory = $false)]
    [ValidateSet("aws", "az", "on-prem")]
    [string]$siteName = "aws",

    # Accept any additional arguments to prevent parameter binding errors
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$RemainingArguments
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message
    )

    $objectReturn = @{
        objectName         = $objectName
        Domain             = $domain
        siteName           = $siteName
        Comment            = $Script:updateComment
        ouCreatedIn        = $ouPath
        timeStamp          = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        clusterNodeComment = $Script:clusterNodeComment
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Test-ADCredentials {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $true)]
        [string]$Domain
    )

    try {
        $null = Get-ADDomain -Server $Domain -Credential $Credential -ErrorAction Stop
        return $true
    }
    catch {
        # Store error info for potential logging but don't output to stream
        Write-Debug "Credential validation failed: $($_.Exception.Message)"
        return $false
    }
}

function Get-IntraSiteDomainController {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $false)]
        [string]$SiteName
    )

    try {
        $dcParams = @{
            Server = $Domain
            Credential = $Credential
            ErrorAction = 'Stop'
        }

        # Handle simplified site names - convert to lowercase for consistency
        $siteNameLower = $SiteName.ToLower()

        # For on-prem, skip intra-site logic and go straight to PDC
        if ($siteNameLower -eq "on-prem") {
            Write-Debug "On-premises deployment specified, skipping intra-site DC selection"
            $domainControllers = $null
        }
        else {
            # Map simplified names to site patterns
            $sitePattern = switch ($siteNameLower) {
                "aws" { "AWS*" }
                "az"  { "AZ*" }
                default { "$SiteName*" }  # Fallback for any other 3-char codes
            }

            Write-Debug "Looking for domain controllers in sites matching pattern: $sitePattern"
            $domainControllers = Get-ADDomainController -Filter * @dcParams | Where-Object {
                $_.Site -like $sitePattern -and $_.Enabled -eq $true
            }
        }

        if ($domainControllers) {
            $selectedDC = $domainControllers | Get-Random
            Write-Debug "Testing connectivity to intra-site DC: $($selectedDC.HostName)"

            try {
                $null = Get-ADDomain -Server $selectedDC.HostName -Credential $Credential -ErrorAction Stop
                Write-Debug "Successfully connected to intra-site DC: $($selectedDC.HostName)"
                return $selectedDC.HostName
            }
            catch {
                Write-Debug "Failed to connect to intra-site DC $($selectedDC.HostName): $($_.Exception.Message)"
            }
        }
        else {
            Write-Debug "No enabled domain controllers found in site: $SiteName"
        }

        # Fallback 1: Try to get the PDC Emulator
        Write-Debug "Falling back to PDC Emulator for domain: $Domain"
        try {
            $pdcEmulator = Get-ADDomainController -Discover -Service PrimaryDC -DomainName $Domain -Credential $Credential -ErrorAction Stop
            if ($pdcEmulator) {
                Write-Debug "Testing connectivity to PDC Emulator: $($pdcEmulator.HostName)"
                try {
                    $null = Get-ADDomain -Server $pdcEmulator.HostName -Credential $Credential -ErrorAction Stop
                    Write-Debug "Successfully connected to PDC Emulator: $($pdcEmulator.HostName)"
                    return $pdcEmulator.HostName
                }
                catch {
                    Write-Debug "Failed to connect to PDC Emulator $($pdcEmulator.HostName): $($_.Exception.Message)"
                }
            }
        }
        catch {
            Write-Debug "Failed to discover PDC Emulator: $($_.Exception.Message)"
        }

        # Fallback 2: Try any available enabled DC
        Write-Debug "Falling back to any available enabled domain controller"
        $allDomainControllers = Get-ADDomainController -Filter * @dcParams | Where-Object { $_.Enabled -eq $true }

        if ($allDomainControllers) {
            foreach ($dc in $allDomainControllers) {
                try {
                    Write-Debug "Testing connectivity to DC: $($dc.HostName)"
                    $null = Get-ADDomain -Server $dc.HostName -Credential $Credential -ErrorAction Stop
                    Write-Debug "Successfully connected to DC: $($dc.HostName)"
                    return $dc.HostName
                }
                catch {
                    Write-Debug "Failed to connect to DC $($dc.HostName): $($_.Exception.Message)"
                    continue
                }
            }
        }

        # Final fallback: Use domain name (DNS resolution)
        Write-Debug "All DC connectivity tests failed, falling back to domain name: $Domain"
        return $Domain
    }
    catch {
        Write-Debug "Failed to retrieve domain controllers: $($_.Exception.Message)"
        return $Domain
    }
}

function Test-ADOUPath {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$OUPath,

        [Parameter(Mandatory = $true)]
        [string]$Server,

        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential
    )

    try {
        # Convert to full DN if needed
        $fullOuPath = if ($OUPath -notlike "*DC=*") {
            $domainDN = ($Domain -split '\.') | ForEach-Object { "DC=$_" }
            "$OUPath,$($domainDN -join ',')"
        }
        else {
            $OUPath
        }

        $null = Get-ADOrganizationalUnit -Identity $fullOuPath -Server $Server -Credential $Credential -ErrorAction Stop
        return $fullOuPath
    }
    catch {
        # Store error info for potential logging but don't output to stream
        Write-Debug "OU path validation failed: $($_.Exception.Message)"
        return $null
    }
}

function Grant-ClusterNodePermissions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [Microsoft.ActiveDirectory.Management.ADComputer]$adObject,

        [Parameter(Mandatory = $true)]
        [string]$clusterNodeList,

        [Parameter(Mandatory = $true)]
        [string]$domainName,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$adCreds
    )

    # Load required assemblies (explicit loading for PowerShell 5.1)
    Add-Type -AssemblyName System.DirectoryServices

    # Split by multiple delimiters: comma, space, or pipe (period removed to support FQDNs)
    $nodeList = $clusterNodeList -split '[,\s\|]+' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

    $successfulNodes = @()
    $failedNodes = @()
    $nodeMessages = @()

    foreach ($node in $nodeList) {
        if (-not [string]::IsNullOrWhiteSpace($node)) {
            try {
                # Get the node computer account
                $nodeAccount = Get-ADComputer -Identity $node -Server $domainName -Credential $adCreds -ErrorAction Stop

                # Create DirectoryEntry with credentials
                $directoryEntry = $null
                try {
                    # Use the domain name as the LDAP server
                    $ldapPath = "LDAP://$domainName/$($adObject.DistinguishedName)"

                    $directoryEntry = New-Object System.DirectoryServices.DirectoryEntry(
                        $ldapPath,
                        $adCreds.UserName,
                        $adCreds.GetNetworkCredential().Password
                    )

                    # Test the connection by accessing a property
                    $null = $directoryEntry.Guid

                    # Get the security descriptor
                    $securityDescriptor = $directoryEntry.ObjectSecurity

                    # Create access rule for GenericAll permissions (required for cluster nodes)
                    $accessRule = New-Object System.DirectoryServices.ActiveDirectoryAccessRule(
                        [System.Security.Principal.SecurityIdentifier]$nodeAccount.SID,
                        [System.DirectoryServices.ActiveDirectoryRights]::GenericAll,
                        [System.Security.AccessControl.AccessControlType]::Allow
                    )

                    # Apply the rule
                    $securityDescriptor.SetAccessRule($accessRule)
                    $directoryEntry.ObjectSecurity = $securityDescriptor
                    $directoryEntry.CommitChanges()

                    $successfulNodes += $node
                    $nodeMessages += "SUCCESS: $node"
                    # Use Write-Debug instead of Write-Verbose for API safety
                    Write-Debug "Successfully granted GenericAll permissions to $node ($($nodeAccount.SID)) on $($adObject.Name)"
                }
                finally {
                    if ($directoryEntry) {
                        $directoryEntry.Dispose()
                    }
                }
            }
            catch {
                $failedNodes += $node
                $nodeMessages += "FAILED: $node - $($_.Exception.Message)"
                # Store warning in variable instead of outputting to stream
                Write-Debug "Failed to grant permissions to $node`: $($_.Exception.Message)"
                # Continue with next node - don't fail the entire operation
            }
        }
    }

    # Return status information for inclusion in the response
    $clusterNodeComment = if ($nodeMessages.Count -gt 0) {
        "Cluster node permissions: " + ($nodeMessages -join "; ")
    } else {
        "No cluster nodes processed"
    }

    return @{
        SuccessfulNodes = $successfulNodes
        FailedNodes = $failedNodes
        Comment = $clusterNodeComment
    }
}

try {
    $Script:updateComment = ""
    $Script:clusterNodeComment = ""
    $jobStatus = "IN_PROGRESS"
    $success = $true
    $accountExists = $false

    if ($vmOS -eq "Linux") {
        $Script:updateComment = "Linux based server, no AD Object created."
        $jobStatus = "COMPLETED"
        return (New-JsonReturn -success $success -status $jobStatus -message $Script:updateComment)
    }

    $credentialTarget = if ($appType -eq "VDI") { "VDI" } else { $domain }

    try {
        $adCreds = Get-StoredCredential -Target $credentialTarget -ErrorAction Stop
        if (-not $adCreds) {
            throw "No credentials found for target: $credentialTarget"
        }
    }
    catch {
        $errorMessage = "Failed to retrieve stored credentials for target '$credentialTarget': $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    if (-not (Test-ADCredentials -Credential $adCreds -Domain $domain)) {
        $errorMessage = "Credential validation failed for domain: $domain. Please check stored credentials."
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    # Convert siteName to lowercase for consistency
    $siteNameLower = $siteName.ToLower()
    $intraDomainController = Get-IntraSiteDomainController -Domain $domain -Credential $adCreds -SiteName $siteNameLower

    $validatedOuPath = Test-ADOUPath -OUPath $ouPath -Server $intraDomainController -Domain $domain -Credential $adCreds
    if (-not $validatedOuPath) {
        $errorMessage = "OU path validation failed: $ouPath. Please verify the organizational unit exists in domain: $domain"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
    $ouPath = $validatedOuPath

    try {
        $null = Get-ADComputer -Identity $objectName -Server $intraDomainController -Credential $adCreds -ErrorAction Stop
        $accountExists = $true
        Write-Debug "AD Computer object '$objectName' already exists"
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        $accountExists = $false
        Write-Debug "AD Computer object '$objectName' does not exist, will create new"
    }
    catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
        $errorMessage = "Unable to reach intra-site domain controller: $intraDomainController for domain: $domain"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
    catch {
        $errorMessage = "Error checking for existing AD object: $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    if (-not $accountExists) {
        try {
            $newAdComputerParams = @{
                Name            = $objectName
                SAMAccountName  = $objectName
                Path            = $ouPath
                Description     = $objectDescription
                Enabled         = $true
                Credential      = $adCreds
                Server          = $intraDomainController
                DNSHostName     = "$objectName.$domain"
                OtherAttributes = @{ 'comment' = $jobId }
            }

            New-ADComputer @newAdComputerParams
            Write-Debug "AD Computer object '$objectName' created successfully"
            Start-Sleep -Seconds 10
        }
        catch {
            $errorMessage = "Failed to create AD computer object: $($_.Exception.Message)"
            return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
        }
    }
    else {
        Write-Debug "AD Computer object '$objectName' already exists, skipping creation"
        $Script:updateComment = "AD Object $objectName already exists in $domain"
    }

    try {
        $adObject = Get-ADComputer -Identity $objectName -Server $intraDomainController -Credential $adCreds -ErrorAction Stop

        # Handle cluster node permissions for CLS and LST objects
        if (($objectName.StartsWith("CLS", [System.StringComparison]::OrdinalIgnoreCase) -or
                $objectName.StartsWith("LST", [System.StringComparison]::OrdinalIgnoreCase)) -and
            -not [string]::IsNullOrWhiteSpace($clusterNodes)) {

            Start-Sleep 10  # Allow AD replication time

            try {
                $clusterResult = Grant-ClusterNodePermissions -adObject $adObject -clusterNodeList $clusterNodes -domainName $intraDomainController -adCreds $adCreds
                $Script:clusterNodeComment = $clusterResult.Comment
                Write-Debug "Cluster node permissions processed: $($clusterResult.Comment)"
            }
            catch {
                # Don't fail the entire operation if cluster permissions fail
                $Script:clusterNodeComment = "Cluster node permissions failed: $($_.Exception.Message)"
                # Store error info for potential logging but don't output to stream
                Write-Debug "Cluster node permissions failed but continuing: $($_.Exception.Message)"
            }
        }

        if ([string]::IsNullOrEmpty($Script:updateComment)) {
            $Script:updateComment = "AD Object $objectName created successfully in $domain"
        }
        $jobStatus = "COMPLETED"
    }
    catch {
        $errorMessage = "Failed to verify AD object creation: $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    # Return success response
    return (New-JsonReturn -success $success -status $jobStatus -message $Script:updateComment)
}
catch {
    $errorMessage = "Unexpected error: $($_.Exception.Message)"
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
