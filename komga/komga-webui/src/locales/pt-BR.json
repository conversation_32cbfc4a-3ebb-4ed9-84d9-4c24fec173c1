{"$vuetify": {"dataFooter": {"pageText": "{0}-{1} de {2}"}, "dataIterator": {"loadingText": "Carregando itens...", "noResultsText": "Nenhum registro correspondente encontrado"}, "dataTable": {"itemsPerPageText": "<PERSON><PERSON> por página:", "sortBy": "Ordenar por"}, "fileInput": {"counter": "{0} arquivo(s)", "counterSize": "{0} arquivo(s) ({1} no total)"}, "noDataText": "Não há dados disponíveis"}, "account_settings": {"account_settings": "Configurações de conta", "api_key": {"created_date": "Data de criação: {date}", "force_kobo_sync": "Forçar sincronização com Kobo", "generate_api_key": "Gerar chave de API", "no_keys": "Nenhuma Chave de API foi criada"}, "change_password": "<PERSON>ar senha", "details": "<PERSON><PERSON><PERSON>", "my_account": "Minha conta"}, "announcements": {"mark_all_read": "Marque todos como lido", "mark_read": "Marque como lido", "tab_title": "<PERSON><PERSON><PERSON><PERSON>"}, "authentication_activity": {"api_key": "Chave de <PERSON>", "datetime": "Data e Hora", "email": "E-mail", "error": "Erro", "ip": "IP", "source": "Origem", "success": "Sucesso", "user_agent": "Agente de Usuário"}, "author_roles": {"colorist": "coloristas", "cover": "Capa", "editor": "editores", "inker": "arte-finalistas", "letterer": "<PERSON><PERSON><PERSON>", "penciller": "desenhistas", "translator": "<PERSON><PERSON><PERSON><PERSON>", "writer": "Escritores"}, "book_card": {"error": "Erro", "no_release_date": "Sem data de lançamento", "unknown": "<PERSON>", "unread": "Não lido", "unsupported": "Não suportado"}, "book_import": {"button_browse": "Navegar", "button_import": "Importar", "button_scan": "Examinar", "button_select_series": "Selecione uma série", "field_import_path": "Importar a partir de uma pasta", "info_part1": "Esta tela permite importar arquivos que estão fora de suas bibliotecas existentes. Você só pode importar arquivos para as séries existentes; nesse caso, Komga moverá ou copiará os arquivos para o diretório da série escolhida.", "info_part2": "Se escolher um número para um livro, e já havendo um livro com este número, então poderá comparar os dois livros. Se decidir importar o livro, Komga vai atualizar o livro, substituindo o arquivo antigo pelo novo.", "no_files_found": "Nenhum arquivo encontrado", "notification": {"go_to_book": "Ir para o livro", "import_failure": "Falha ao importar livro: {file}", "import_successful": "Livro importado com sucesso: {book}", "source_file": "Arquivo de origem: {file}"}, "row": {"error_analyze_first": "O livro deve ser analisado primeiro", "error_choose_series": "Escolha uma série", "error_only_import_no_errors": "Só se pode importar livros sem erros", "warning_upgrade": "O livro existente será atualizado"}, "table": {"destination_name": "Nome de destino", "file_name": "Nome do arquivo", "number": "Número", "series": "Série"}, "title": "Importar", "try_another_directory": "Tente pesquisar outro diretório"}, "bookreader": {"beginning_of_book": "Você está no inicio do livro.", "changing_reading_direction": "Mudar Direção de Leitura para", "cycling_page_layout": "Alterando Layout de Página", "cycling_page_margin": "Alterando Margem de <PERSON>gina", "cycling_scale": "Alterando <PERSON>", "cycling_side_padding": "Alterando Preenchimento Lateral", "download_current_page": "Baixar a pagina atual", "end_of_book": "Você chegou ao final do livro.", "from_series_metadata": "dos metadados da série", "move_next": "Clique ou pressione em \"Próximo\" novamente para mover ao próximo livro.", "move_next_exit": "Clique ou pressione em \"Próximo\" novamente para sair do leitor.", "move_previous": "Clique ou pressione em \"Anterior\" novamente para mover ao livro anterior.", "notification_poster_set_book": "O livro pôster agora está definido para a página atual.", "notification_poster_set_readlist": "A lista de leitura pôster agora está definida para a página atual.", "notification_poster_set_series": "O pôster da série está definido agora para a página atual.", "paged_reader_layout": {"double": "<PERSON><PERSON><PERSON><PERSON>", "double_no_cover": "<PERSON><PERSON><PERSON><PERSON> dup<PERSON> (sem capa)", "single": "Página simples"}, "reader_settings": "Configurações de Leitura", "scale_type": {"continuous_original": "Original", "continuous_width": "Ajustar à largura", "height": "Ajustar à altura", "original": "Original", "screen": "Tela", "width": "Ajustar à largura", "width_shrink_only": "Ajustar à largura (encolher apenas)"}, "set_current_page_as_book_poster": "Definir a página como pôster do livro", "set_current_page_as_readlist_poster": "Definir a página como pôster para a lista de leitura", "set_current_page_as_series_poster": "Definir página como poster da série", "settings": {"always_fullscreen": "Sempre em Tela Cheia", "animate_page_transitions": "Animar as transições de página", "background_color": "Cor de fundo", "background_colors": {"black": "Preto", "gray": "Cinza", "white": "Branco"}, "display": "Exibição", "general": "G<PERSON>", "gestures": "Gestos", "page_layout": "Layout de página", "page_margin": "<PERSON><PERSON><PERSON>", "paged": "Opções do Reader paginado", "reading_mode": "Modo de leitura", "scale_type": "Tipo de escala", "side_padding": "Margem lateral", "side_padding_none": "Sem categoria", "webtoon": "Opções do Webtoon Reader"}, "shortcuts": {"close": "<PERSON><PERSON><PERSON>", "cycle_page_layout": "Alterar layout de página", "cycle_page_margin": "<PERSON><PERSON><PERSON> marge<PERSON> de <PERSON>", "cycle_scale": "Alterar escala", "cycle_side_padding": "Alterar preenchimento lateral", "first_page": "Primeira página", "fullscreen": "Entrar/sair da tela cheia", "last_page": "Última página", "left_to_right": "Esquerda para Direita", "menus": "Menus", "next_page": "Próxima página", "previous_page": "Página anterior", "reader_navigation": "Navegação no Leitor", "right_to_left": "Direita para Esquerda", "settings": "Configurações", "show_hide_help": "Mostrar/ocultar ajuda", "show_hide_settings": "Mostrar/ocultar menu de configuração", "show_hide_thumbnails": "Mostrar/ocultar navegador de miniaturas", "show_hide_toolbars": "Mostrar/ocultar barras de ferramentas", "vertical": "Vertical", "webtoon": "Webtoon"}, "tooltip_incognito": "O progresso da leitura não será salvo"}, "browse_book": {"comment": "COMENTÁRIO", "date_created": "CRIADO", "date_modified": "MODIFICADO POR ÚLTIMO", "download_file": "Baixar arquivo", "file": "ARQUIVO", "format": "FORMATO", "isbn": "ISBN", "links": "Links", "navigation_within_readlist": "Navegação dentro da lista de leitura: {name}", "outdated_tooltip": "O arquivo para este livro foi alterado, este livro deve ser reanalisado", "read_book": "Ler livro", "read_incognito": "<PERSON><PERSON> incógnito", "remove_from_collection": "Remover livro da coleção", "remove_from_readlist": "Remover livro da lista de leitura", "size": "TAMANHO"}, "browse_collection": {"edit_collection": "<PERSON><PERSON>", "edit_elements": "<PERSON><PERSON>", "manual_ordering": "ordenação manual"}, "browse_readlist": {"edit_elements": "<PERSON><PERSON>", "edit_readlist": "Editar lista de leitura", "manual_ordering": "Ordenação manual"}, "browse_series": {"earliest_year_from_release_dates": "Este é o ano mais antigo dentre as datas de lançamento de todos os livros na série", "remove_from_collection": "Remover série da coleção", "series_no_summary": "Esta série não contém um resumo, então escolhemos um para você!", "summary_from_book": "Resumo do livro {number}:"}, "collections_expansion_panel": {"manage_collection": "Gerenciar coleção", "title": "Coleção {name}"}, "common": {"age": "<PERSON><PERSON>", "all_libraries": "Todas as bibliotecas", "all_of": "Todos", "any_of": "<PERSON>ual<PERSON>", "book": "<PERSON><PERSON>", "books": "<PERSON><PERSON>", "books_n": "Nenhum livro | 1 livro | {count} livros", "books_total": "{count} / {total} livros", "cancel": "<PERSON><PERSON><PERSON>", "cbx": "Arquivos de Histórias em Quadrinhos", "choose_image": "Escolher uma imagem", "close": "<PERSON><PERSON><PERSON>", "collections": "Coleções", "copied": "Copiado!", "create": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "dimension": "l: {width}, a:{height}", "discard": "Descar<PERSON>", "disk_space": "Espaço em disco", "dismiss": "<PERSON><PERSON><PERSON>", "download": "Baixar", "drag_drop": "Arrastar e soltar", "duplicate": "Dup<PERSON><PERSON>", "email": "Email", "epub": "Epub", "error": "Erro", "filename": "Nome do arquivo", "filter_no_matches": "O filtro ativo não contém correspondências", "genre": "<PERSON><PERSON><PERSON><PERSON>", "go_to_collection": "<PERSON>r para a coleção", "go_to_library": "Ir para biblioteca", "go_to_readlist": "Ir para lista de lidos", "go_to_series": "Ir para séries", "i_understand": "Eu compreendo", "library": "Biblioteca", "locale_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "locale_rtl": "false", "lock_all": "Bloquear todos", "media": "Mí<PERSON>", "more": "<PERSON><PERSON>", "n_selected": "{count} selecionados", "nothing_to_show": "Nada para exibir", "ok": "OK", "oneshot": "One-shot", "outdated": "Desatualizado", "page": "<PERSON><PERSON><PERSON><PERSON>", "page_number": "Número da página", "pages": "p<PERSON><PERSON><PERSON>", "pages_left": "Nenhuma página faltando | 1 página faltando | {count} páginas faltando", "pages_n": "Nenhuma página | 1 página | {count} páginas", "password": "<PERSON><PERSON>", "pdf": "PDF", "pending_tasks": "<PERSON><PERSON>huma tarefa pendente | 1 tarefa pendente | {count} tarefas pendentes", "pinned_libraries": "Bibliotecas Fixadas", "publisher": "Editora", "read": "<PERSON>r", "read_on": "<PERSON>do em {date}", "readlist": "Lista de Leitura", "readlists": "Listas de Leitura", "remember-me": "Me lembre", "reorder": "<PERSON><PERSON><PERSON><PERSON>", "required": "Obrigatório", "reset_filters": "Redefinir filt<PERSON>", "roles": "Funções", "save_changes": "<PERSON><PERSON>", "series": "Séries | Séries", "settings": "Configurações", "sidecars": "<PERSON><PERSON><PERSON>", "tags": "Tags", "ui": "Interface de Usuário", "unavailable": "Indisponível", "unlock_all": "Desb<PERSON><PERSON>ar tudo", "url": "URL", "use_filter_panel_to_change_filter": "Utilize o painel de filtros para mudar o filtro ativo", "year": "ano"}, "dashboard": {"keep_reading": "<PERSON><PERSON><PERSON><PERSON>", "on_deck": "Na Sequência", "recently_added_books": "Livros <PERSON>", "recently_added_series": "Séries Adicionadas Recentemente", "recently_read_books": "Livros <PERSON> Recentemente", "recently_released_books": "Livros <PERSON> Recentemente", "recently_updated_series": "Séries Atualizadas Recentemente"}, "data_import": {"book_number": "Número de livro: {name}", "book_series": "Série: {name}", "button_import": "Importar", "button_match": "Igualar", "comicrack_preambule_html": "Você pode importar Listas de Leitura do ComicRack existentes no formato <code>.cbl</code><br>Komga tentará combinar a série fornecida e o número do livro com as séries e livros em suas bibliotecas.", "dialog_confirmation": {"body": "{unmatched} / {total} livro(s) não foram marcados", "body2": "{duplicates} / {total} livros são duplicados", "create": "<PERSON><PERSON><PERSON> de todo modo", "title": "Alguns livros não estão combinados"}, "field_file_label": "Lista de Leitura ComicRack (.cbl)", "field_files_label": "Listas de Leitura do ComicRack (.cbl)", "import_read_lists": "Importar Listas de Leitura", "imported_as": "Importado como {name}", "readlist_created": "Lista de leitura criada:{name}", "requested_number": "Número solicitado", "requested_series": "Série solicitada", "results_preambule": "Resultado da importação é mostrado abaixo. Você também pode verificar os livros incomparáveis para cada arquivo fornecido.", "size_limit": "<PERSON><PERSON><PERSON> deve ser menor que {size} MB", "tab_title": "Importação de Dados"}, "dialog": {"add_api_key": {"button_confirm": "<PERSON><PERSON><PERSON>"}, "add_to_collection": {"button_create": "<PERSON><PERSON><PERSON>", "card_collection_subtitle": "Nenhuma série | 1 série | {count} série", "dialog_title": "Adicionar à coleção", "field_search_create": "Buscar ou criar coleção", "field_search_create_error": "Já existe uma coleção com este nome", "label_no_matching_collection": "Nenhuma coleção correspondente"}, "add_to_readlist": {"button_create": "<PERSON><PERSON><PERSON>", "card_readlist_subtitle": "Nenhum livro | 1 livro | {count} livros", "dialog_title": "Adicionar à lista de leitura", "field_search_create": "Buscar ou criar lista de leitura", "field_search_create_error": "Já existe uma lista de leitura com este nome", "label_no_matching_readlist": "Nenhuma lista de leitura correspondente"}, "add_user": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON><PERSON><PERSON>", "dialog_title": "<PERSON><PERSON><PERSON><PERSON>", "field_email": "Email", "field_email_error": "Precisa ser um endereço de email válido", "field_password": "<PERSON><PERSON>"}, "analyze_library": {"body": "<PERSON><PERSON><PERSON> todos os arquivos de mídia da biblioteca. A análise captura informações sobre a mídia. Dependendo do tamanho da sua biblioteca, isso pode levar muito tempo.", "button_confirm": "<PERSON><PERSON><PERSON>", "title": "Analisar biblioteca"}, "book_picker": {"filter": "Filtrar por número, título ou data de lançamento", "title": "Selecionar livro"}, "delete_book": {"button_confirm": "Excluir", "confirm_delete": "Sim, exclua o livro \"{name}\" e seus arquivos", "confirm_delete_multiple": "Sim, exclua {count} livros e seus arquivos", "dialog_title": "Excluir <PERSON>", "dialog_title_multiple": "Excluir <PERSON>", "warning_html": "O livro <b>{name}</b> será removido deste servidor junto com os arquivos de mídia armazenados. Essa ação <b>não</b> pode ser desfeita. Continuar?", "warning_multiple_html": "{count} livros serão removidos deste servidor junto com os arquivos de mídia armazenados. Essa ação <b>não</b> pode ser desfeita. Continuar?"}, "delete_collection": {"button_confirm": "Excluir", "confirm_delete": "<PERSON>m, excluir a coleção \"{name}\"", "confirm_delete_multiple": "<PERSON>m, excluir {count} coleç<PERSON><PERSON>", "dialog_title": "Excluir <PERSON>", "dialog_title_multiple": "Excluir <PERSON>", "warning_html": "A coleção <b>{name}</b> será removida deste servidor. Seus arquivos originais não serão afetados. Isso <b>não poderá</b> ser desfeito. Prosseguir?", "warning_multiple_html": "{count} coleções serão removidas deste servidor. Seus arquivos originais não serão afetados. Isso <b>não poderá</b> ser desfeito. Prosseguir?"}, "delete_library": {"button_confirm": "Excluir", "confirm_delete": "Sim, excluir a biblioteca \"{name}\"", "title": "Excluir Biblioteca", "warning_html": "A biblioteca <b>{name}</b> será removida deste servidor. Seus arquivos originais não serão afetados. Isso <b>não poderá</b> ser desfeito. Prosseguir?"}, "delete_readlist": {"button_confirm": "Excluir", "confirm_delete": "<PERSON>m, excluir a lista de leitura \"{name}\"", "confirm_delete_multiple": "<PERSON><PERSON>, excluir {count} listas de leitura", "dialog_title": "Excluir Lista de Leitura", "dialog_title_multiple": "Excluir Listas de Leitura", "warning_html": "A lista de leitura <b>{name}</b> será removida deste servidor. Seus arquivos originais não serão afetados. Isso <b>não poderá</b> ser desfeito. Prosseguir?", "warning_multiple_html": "{count} listas de leitura serão removidas deste servidor. Seus arquivos originais não serão afetados. Isso <b>não poderá</b> ser desfeito. Prosseguir?"}, "delete_series": {"button_confirm": "Excluir", "confirm_delete": "Sim, exclua a série \"{name}\" e seus arquivos", "confirm_delete_multiple": "Sim, exclua {count} séries e seus arquivos", "dialog_title": "Excluir Série", "warning_html": "A edição <b>{name}</b> será removida deste servidor junto aos arquivos de mídia armazenados. Essa ação <b>não</b> pode ser desfeita. Continuar?", "warning_multiple_html": "{count} edições serão removidas deste servidor junto aos arquivos de mídia armazenados. Essa ação <b>não pode</b> ser desfeita. Continuar?"}, "delete_user": {"button_confirm": "Excluir", "confirm_delete": "<PERSON>m, excluir o usuário \"{name}\"", "dialog_title": "Excluir <PERSON>", "warning_html": "O usuário <b>{name}</b> será excluído deste servidor. <PERSON><PERSON> <b>não poderá</b> ser desfeito. Prosseguir?"}, "edit_books": {"add_author_role_error_duplicate": "<PERSON><PERSON>", "authors_notice_multiple_edit": "Você está editando autores para múltiplos livros. Is<PERSON> irá sobrescrever os autores existentes para cada livro.", "button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON>", "copy_from": "<PERSON><PERSON><PERSON> {field}", "dialog_title_multiple": "Editar {count} livro | Editar {count} livros", "dialog_title_single": "Editar {book}", "field_alternate_title": "Título alternativo", "field_isbn": "ISBN", "field_isbn_error": "<PERSON><PERSON> ser um ISBN 13 valido", "field_link_label": "<PERSON><PERSON><PERSON><PERSON>", "field_link_url": "URL", "field_link_url_error_protocol": "<PERSON><PERSON> ser http ou https", "field_link_url_error_url": "Deve ser uma URL válida", "field_number": "Número", "field_number_sort": "Número para Ordenação", "field_number_sort_hint": "Você pode usar números decimais", "field_release_date": "Data de Lançamento", "field_release_date_error": "Precisa ser uma data válida no formato AAAA-MM-DD", "field_summary": "Resumo", "field_tags": "Tags", "field_title": "<PERSON><PERSON><PERSON><PERSON>", "number_sort_decrement": "Decrementar todos por 1", "number_sort_increment": "Incrementar todos por 1", "tab_authors": "Autores", "tab_general": "G<PERSON>", "tab_links": "Links", "tab_poster": "Pôster", "tab_tags": "Tags", "tags_notice_multiple_edit": "Você está editando tags para múltiplos livros. Isso irá sobrescrever os tags existentes para cada livro."}, "edit_collection": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON>", "dialog_title": "<PERSON><PERSON>", "field_manual_ordering": "Ordenação manual", "label_ordering": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, séries em uma coleção serão ordenadas por nome. Você pode ativar a ordenação manual para definir sua própria ordem.", "tab_general": "G<PERSON>", "tab_poster": "Pôster"}, "edit_library": {"button_browse": "Navegar", "button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm_add": "<PERSON><PERSON><PERSON><PERSON>", "button_confirm_edit": "<PERSON><PERSON>", "button_next": "Próximo", "dialog_title_add": "Adicionar Biblioteca", "dialot_title_edit": "Editar Biblioteca", "field_analysis_analyze_dimensions": "<PERSON><PERSON><PERSON> as dimensões das paginas", "field_analysis_hash_files": "Computar hash para arquivos", "field_analysis_hash_pages": "Computar hash para páginas", "field_convert_to_cbz": "Converter automaticamente para CBZ", "field_import_barcode_isbn": "<PERSON>ó<PERSON>", "field_import_comicinfo_book": "Metadados de livros", "field_import_comicinfo_collections": "Coleções", "field_import_comicinfo_readlists": "Listas de leitura", "field_import_comicinfo_series": "Metadados de séries", "field_import_comicinfo_series_append_volume": "Anexar volume para o título da série", "field_import_epub_book": "Metadados de livros", "field_import_epub_series": "Metadados de séries", "field_import_local_artwork": "Mídia local", "field_import_mylar_series": "Metadados de séries", "field_name": "Nome", "field_repair_extensions": "Reparar extensões de arquivo incorretas automaticamente", "field_root_folder": "Pasta raíz", "field_scan_interval": "Intervalo de scan", "field_scanner_empty_trash_after_scan": "Esvazie a lixeira automaticamente após cada verificação", "field_scanner_force_directory_modified_time": "Forçar data de modificação da pasta", "field_scanner_scan_startup": "Scanear ao iniciar", "field_series_cover": "Capa da série", "file_browser_dialog_button_confirm": "Selecione", "file_browser_dialog_title": "Pasta raiz da biblioteca", "label_analysis": "<PERSON><PERSON><PERSON><PERSON>", "label_file_management": "Gerenciamento de Arquivo", "label_import_barcode_isbn": "Importar ISBN no código de barra", "label_import_comicinfo": "Importar metadados para CBR/CBZ que contenham um arquivo ComicInfo.xml", "label_import_epub": "Importar metadados de arquivos EPUB", "label_import_local": "Importar recursos de mídia locais", "label_import_mylar": "Importar metadados gerados por Mylar", "label_scan_directory_exclusions": "Exclusões de diretórios", "label_scan_types": "Scanear para esses tipos de arquivos", "label_scanner": "<PERSON><PERSON><PERSON> var<PERSON>ura", "label_series_cover": "Capa da série", "tab_general": "G<PERSON>", "tab_metadata": "Metadados", "tab_options": "Opções", "tooltip_oneshotsdirectory": "Deixar vazio para desabilitar", "tooltip_scanner_force_modified_time": "Habilitar se uma biblioteca está em um Google Drive", "tooltip_use_resources": "Pode consumir muitos recursos em bibliotecas grandes ou hardware lento"}, "edit_readlist": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON>", "dialog_title": "Editar lista de leitura", "field_manual_ordering": "Ordenação manual", "field_name": "Nome", "field_summary": "Resumo", "label_ordering": "<PERSON><PERSON> <PERSON><PERSON>, livros em uma lista de leitura são ordenados manualmente. Você pode desativar a ordenação manual para ordenar os livros por data de lançamento.", "tab_general": "G<PERSON>", "tab_poster": "Pôster"}, "edit_series": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON>", "dialog_title_multiple": "Editar {count} série | Editar {count} séries", "dialog_title_single": "Editar {series}", "field_age_rating": "Classificação Etária", "field_age_rating_error": "Classificação etária precisa ser igual ou maior que 0", "field_genres": "<PERSON><PERSON><PERSON><PERSON>", "field_labels": "<PERSON><PERSON><PERSON><PERSON>", "field_language": "Idioma", "field_language_hint": "Tag de idioma IETF BCP 47", "field_publisher": "Editora", "field_reading_direction": "Direção de Leitura", "field_sort_title": "Título para Ordenação", "field_status": "Estado", "field_summary": "Resumo", "field_tags": "Tags", "field_title": "<PERSON><PERSON><PERSON><PERSON>", "field_total_book_count": "Contagem total de livro", "field_total_book_count_error": "A contagem total de livro deve ser 1 ou mais", "mixed": "MISTURADO", "tab_general": "G<PERSON>", "tab_poster": "Pôster", "tab_sharing": "Compartil<PERSON><PERSON>", "tab_tags": "Tags", "tab_titles": "Títulos alternativos", "tags_notice_multiple_edit": "Você está editando tags para múltiplas séries. Isso irá sobrescrever os tags existentes para cada série."}, "edit_user": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON>", "dialog_title": "<PERSON><PERSON>", "label_roles_for": "Cargos para {name}"}, "edit_user_restrictions": {"age_restriction": {"allow_under": "<PERSON><PERSON><PERSON> a<PERSON> de", "exclude_over": "Excluir acima de", "none": "Sem restrição"}, "edit_restrictions_for": "Editar restrições para {name}", "label_age_restriction": "Restrição de idade", "label_allow_only_labels": "<PERSON><PERSON><PERSON>", "label_exclude_labels": "<PERSON>cluir <PERSON>", "tab_content_restrictions": "Restrições de conteúdo", "tab_shared_libraries": "Bibliotecas Compartilhadas"}, "empty_trash": {"body": "<PERSON>r pad<PERSON><PERSON>, o servidor de mídia não remove as informações da mídia imediatamente. Isso ajuda se uma unidade estiver temporariamente desconectada. Quando você esvazia a lixeira de uma biblioteca, todas as informações sobre mídia ausente são excluídas.", "button_confirm": "<PERSON><PERSON><PERSON>", "title": "Lixeira vazia para biblioteca"}, "file_browser": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm_default": "Selecione", "dialog_title_default": "Navegador de Arquivos", "parent_directory": "Pasta superior"}, "filename_chooser": {"button_choose": "Selecionar", "field_destination_filename": "Nome do Arquivo de Destino", "label_source_filename": "Nome do Arquivo de Origem", "table": {"existing_file": "Arquivo Existente", "order": "<PERSON>nar"}, "title": "Nome do Arquivo de Destino"}, "password_change": {"button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON>", "dialog_title": "<PERSON><PERSON>", "field_new_password": "Nova senha", "field_new_password_error": "É necessária uma nova senha.", "field_repeat_password": "<PERSON>ita a nova senha", "field_repeat_password_error": "As senhas devem ser idênticas."}, "refresh_library_metadata": {"body": "Atualiza os metadados de todos os arquivos de mídia da biblioteca. Dependendo do tamanho da sua biblioteca, isso pode levar muito tempo.", "button_confirm": "<PERSON><PERSON><PERSON><PERSON>", "title": "Atualizar metadados para biblioteca"}, "series_picker": {"label_search_series": "Buscar Séries", "title": "Selecionar Série"}, "server_stop": {"button_confirm": "<PERSON><PERSON>", "confirmation_message": "Tem certeza que deseja parar o Komga?", "dialog_title": "<PERSON><PERSON><PERSON> servidor"}, "shortcut_help": {"label_description": "Descrição", "label_key": "Tecla"}, "transient_book_details": {"label_candidate": "Candi<PERSON><PERSON>", "label_existing": "Existente", "label_format": "Formato", "label_name": "Nome", "label_pages": "<PERSON><PERSON><PERSON><PERSON>", "label_size": "<PERSON><PERSON><PERSON>", "pages_table": {"filename": "Nome do arquivo", "height": "Altura", "index": "<PERSON><PERSON><PERSON>", "media_type": "Tipo de mídia", "size": "<PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>"}, "title": "Detalhes do Livro", "title_comparison": "Comparação de Livros"}, "transient_book_viewer": {"label_candidate": "Candi<PERSON><PERSON>", "label_existing": "Existente", "page_of_pages": "{page} / {pages}", "title": "Inspecionar Livro", "title_comparison": "Comparação de Livros"}}, "duplicate_pages": {"filter": {"size": "<PERSON><PERSON><PERSON>"}}, "duplicates": {"size": "<PERSON><PERSON><PERSON>", "url": "URL"}, "enums": {"copy_mode": {"HARDLINK": "Hardlink/Copiar arquivos", "MOVE": "Mover <PERSON>"}, "media_status": {"ERROR": "Erro", "OUTDATED": "Desatualizado", "READY": "Pronto", "UNKNOWN": "Desconhecido", "UNSUPPORTED": "Não Suportado"}, "reading_direction": {"LEFT_TO_RIGHT": "Esquerda para direita", "RIGHT_TO_LEFT": "Direita para esquerda", "VERTICAL": "Vertical", "WEBTOON": "Webtoon"}, "series_cover": {"FIRST": "<PERSON><PERSON>", "FIRST_UNREAD_OR_FIRST": "Primeiro não lido ou primeiro", "FIRST_UNREAD_OR_LAST": "Primeiro não lido ou último", "LAST": "Último"}, "series_status": {"ABANDONED": "<PERSON><PERSON><PERSON><PERSON>", "ENDED": "Finalizado", "HIATUS": "Interrompido", "ONGOING": "Em andamento"}}, "error_codes": {"ERR_1000": "O arquivo não pôde ser acessado durante a análise", "ERR_1001": "O tipo de mídia não é compatível", "ERR_1002": "Arquivos RAR criptografados não são suportados", "ERR_1003": "Arquivos RAR sólidos não são suportados", "ERR_1004": "Arquivos RAR de vários volumes não são suportados", "ERR_1005": "Erro desconhecido ao analisar o livro", "ERR_1006": "O livro não contém nenhuma página", "ERR_1007": "Algumas entradas não puderam ser analisadas", "ERR_1008": "Erro desconhecido ao obter entradas do livro", "ERR_1009": "Já existe uma lista de leitura com esse nome", "ERR_1015": "Erro ao desserializar ComicRack CBL", "ERR_1016": "Diretório não acessível ou não é um diretório", "ERR_1017": "Não é possível digitalizar a pasta que faz parte de uma biblioteca existente", "ERR_1018": "Arquivo não encontrado", "ERR_1019": "Não é possível importar o arquivo que faz parte de uma biblioteca existente", "ERR_1020": "O livro para atualizar não pertence à série fornecida", "ERR_1021": "O arquivo de destino já existe", "ERR_1022": "Não foi possível digitalizar o livro recém-importado", "ERR_1023": "Livro já presente na Lista de Leitura", "ERR_1024": "Erro de login OAuth2: nenhum atributo de e-mail", "ERR_1025": "Erro de login OAuth2: não existe nenhum usuário local com esse e-mail", "ERR_1026": "Erro de login do OpenID Connect: e-mail não verificado"}, "filter": {"age_rating": "classificação etária", "age_rating_none": "<PERSON><PERSON><PERSON><PERSON>", "genre": "<PERSON><PERSON><PERSON><PERSON>", "in_progress": "Em progresso", "language": "idioma", "library": "biblioteca", "publisher": "editora", "read": "Lidos", "release_date": "data de lançamento", "status": "estado", "tag": "tag", "unread": "Não Lidos"}, "filter_drawer": {"filter": "filtro", "sort": "ordenar"}, "history": {"header": {"book": "<PERSON><PERSON>"}}, "home": {"theme": "<PERSON><PERSON>", "translation": "Tradução"}, "library_navigation": {"browse": "Navegar", "collections": "Coleções", "readlists": "Listas de Leitura", "recommended": "Recomendado"}, "login": {"create_user_account": "Criar conta de usuário", "login": "Entrar", "unclaimed_html": "Este servidor <PERSON><PERSON> ainda não está ativo, você precisa criar uma conta de usuário para poder acessá-lo.<br><br><PERSON><PERSON><PERSON><PERSON> um <strong>email</strong> e <strong>senha</strong> e clique em <strong>Criar conta de usuário</strong>."}, "media_analysis": {"comment": "<PERSON><PERSON><PERSON><PERSON>", "media_analysis": "<PERSON><PERSON><PERSON><PERSON> mí<PERSON>", "media_type": "Tipo de mídia", "name": "Nome", "size": "<PERSON><PERSON><PERSON>", "status": "Estado", "url": "URL"}, "menu": {"add_to_collection": "Adicionar à coleção", "add_to_readlist": "Adicionar à lista de leitura", "analyze": "<PERSON><PERSON><PERSON>", "bulk_edit_metadata": "Metadados de edição em massa", "delete": "Excluir", "deselect_all": "<PERSON><PERSON><PERSON> tudo", "download_readlist": "Baixar lista de leitura", "download_series": "Baixar série", "edit": "<PERSON><PERSON>", "edit_metadata": "Editar <PERSON>", "empty_trash": "Esvaziar a lixeira", "mark_read": "Marcar como lido", "mark_unread": "Marcar como não lido", "refresh_metadata": "Atualizar <PERSON>", "scan_library_files": "Varrer arquivos na biblioteca", "select_all": "Selecionar to<PERSON>"}, "navigation": {"home": "Início", "libraries": "Bibliotecas", "logout": "<PERSON><PERSON>"}, "page_not_found": {"go_back_to_home_page": "Retornar à página inicial", "page_does_not_exist": "A página que você procura não existe.", "page_not_found": "Página não encontrada"}, "read_more": {"less": "<PERSON>er menos", "more": "Ver mais"}, "readlists_expansion_panel": {"manage_readlist": "Gerenciar lista de leitura", "title": "Lista de leitura: {name}"}, "search": {"no_results": "A busca não encontrou resultados", "search": "Busca", "search_for_something_else": "Tente buscar por outros termos", "search_results_for": "Buscar resultados para \"{name}\""}, "searchbox": {"in_library": "em {library}", "no_results": "Sem resultados", "search_all": "P<PERSON><PERSON><PERSON>…"}, "server": {"server_management": {"button_cancel_all_tasks": "<PERSON><PERSON><PERSON> to<PERSON> as tare<PERSON>s", "button_empty_trash": "Limpar a lixeira de todas as bibliotecas", "button_scan_libraries": "Escanear todas as bibliotecas", "button_shutdown": "<PERSON><PERSON><PERSON>", "notification_tasks_cancelled": "Nenhuma tarefa para cancelar | Uma tarefa cancelada | {count} tarefas canceladas", "section_title": "Gerenciamento do Servidor"}, "tab_title": "<PERSON><PERSON><PERSON>"}, "server_settings": {"server_settings": "Configurações do Servidor"}, "settings_user": {"change_password": "<PERSON><PERSON><PERSON><PERSON>", "edit_user": "<PERSON><PERSON>", "latest_activity": "Última atividade: {date}", "no_recent_activity": "Nenhuma atividade recente", "role_administrator": "Administrador", "role_user": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"books_count": "Total de livros", "date_added": "Data de adição", "date_updated": "Data de atualização", "file_name": "Nome do arquivo", "file_size": "Tamanho do arquivo", "folder_name": "Nome da pasta", "name": "Nome", "number": "Número", "release_date": "Data de lançamento"}, "theme": {"dark": "Escuro", "light": "<PERSON><PERSON><PERSON>", "system": "Sistema"}, "thumbnail_card": {"tooltip_delete": "Excluir", "tooltip_mark_as_selected": "Marcar como selecionado", "tooltip_selected": "Selecionado", "tooltip_sidecar": "Mídia local", "tooltip_to_be_deleted": "Para ser deletado", "tooltip_to_be_uploaded": "Para ser enviado", "tooltip_too_big": "Arquivo muito grande!", "tooltip_user_uploaded": "Enviado pelo usuário"}, "user_roles": {"ADMIN": "Administrador", "FILE_DOWNLOAD": "Baixar arquivos", "PAGE_STREAMING": "Streaming de páginas", "USER": "<PERSON><PERSON><PERSON><PERSON>"}, "users": {"authentication_activity": "Atividade de Autenticação", "users": "Usuários"}, "welcome": {"add_library": "Adicionar biblioteca", "no_libraries_yet": "Ainda não foram adicionadas bibliotecas!", "welcome_message": "Bem-vindo ao <PERSON>"}}