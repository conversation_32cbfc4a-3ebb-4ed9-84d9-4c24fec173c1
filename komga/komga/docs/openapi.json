{"openapi": "3.1.0", "info": {"description": "Komga REST API.\n\n## Reference\n\nCheck the API reference:\n- on the [Komga website](https://komga.org/docs/openapi/komga-api)\n- on any running Komga instance at `/swagger-ui.html`\n- on [GitHub](https://raw.githubusercontent.com/gotson/komga/refs/heads/master/komga/docs/openapi.json)\n\n## Authentication\n\nMost endpoints require authentication. Authentication is done using either:\n- Basic Authentication\n- Passing an API Key in the `X-API-Key` header\n\n## Sessions\n\nUpon successful authentication, a session is created, and can be reused.\n\n- By default, a `KOMGA-SESSION` cookie is set via `Set-Cookie` response header. This works well for browsers and clients that can handle cookies.\n- If you specify a header `X-Auth-Token` during authentication, the session ID will be returned via this same header. You can then pass that header again for subsequent requests to reuse the session.\n\nIf you need to set the session cookie later on, you can call `/api/v1/login/set-cookie` with `X-Auth-Token`. The response will contain the `Set-Cookie` header.\n\n## Remember Me\n\nDuring authentication, if a request parameter `remember-me` is passed and set to `true`, the server will also return a `komga-remember-me` cookie. This cookie will be used to login automatically even if the session has expired.\n\n## Logout\n\nYou can explicitly logout an existing session by calling `/api/logout`. This would return a `204`.\n\n## Deprecation\n\nAPI endpoints marked as deprecated will be removed in the next major version.", "license": {"name": "MIT", "url": "https://github.com/gotson/komga/blob/master/LICENSE"}, "title": "Komga API", "version": "1.23.5"}, "externalDocs": {"description": "Komga documentation", "url": "https://komga.org"}, "servers": [{"url": "https://demo.komga.org", "description": "Demo server"}, {"url": "http://localhost:{port}", "description": "Local development server", "variables": {"port": {"enum": ["8080", "25600"], "default": "25600"}}}], "security": [{"basicAuth": []}, {"apiKey": []}], "tags": [{"name": "Deprecated"}, {"description": "Manage libraries.", "name": "Libraries"}, {"description": "Manage series.", "name": "Series"}, {"description": "Manage posters for series.", "name": "Series Poster"}, {"description": "Manage books.", "name": "Books"}, {"description": "Manage posters for books.", "name": "Book Poster"}, {"name": "Book Pages"}, {"name": "WebPub Manifest"}, {"name": "Import"}, {"description": "Provide font files and CSS for the Epub Reader.", "name": "Fonts"}, {"description": "Manage duplicate pages. Duplicate pages are identified by a page hash.", "name": "Duplicate Pages"}, {"description": "Manage collections.", "name": "Collections"}, {"description": "Manage posters for collections.", "name": "Collection Poster"}, {"name": "Collection Series"}, {"description": "Manage readlists.", "name": "Readlists"}, {"description": "Manage posters for readlists.", "name": "Readlist Poster"}, {"name": "Readlist Books"}, {"description": "Retrieve referential metadata from all items in the Komga server.", "name": "Referential metadata"}, {"description": "Manage current user.", "name": "Current user"}, {"description": "Manage users.", "name": "Users"}, {"description": "Manage API Keys", "name": "API Keys"}, {"name": "User session"}, {"description": "List registered OAuth2 providers", "name": "OAuth2"}, {"description": "Sync points are automatically created during a Kobo sync.", "name": "Sync points"}, {"description": "Claim a freshly installed Komga server.", "name": "<PERSON><PERSON><PERSON>"}, {"description": "Manage server tasks", "name": "Tasks"}, {"description": "Server events history", "name": "History"}, {"description": "List files from the host server's file system", "name": "File system"}, {"description": "Store and retrieve server settings", "name": "Server settings"}, {"description": "Retrieve releases information", "name": "Releases"}, {"description": "Retrieve announcements from the Komga website", "name": "Announcements"}, {"description": "Manage server", "name": "Management"}, {"name": "<PERSON><PERSON>"}, {"name": "ComicRack"}, {"description": "Store and retrieve global and per-user settings. Those settings are not used by Komga itself, but can be stored for convenience by client applications.", "name": "Client settings"}], "paths": {"/actuator/info": {"get": {"description": "Required role: **ADMIN**", "operationId": "getActuatorInfo", "responses": {"200": {"content": {"application/json": {"examples": {"Example": {"value": "{\n  \"git\": {\n    \"branch\": \"master\",\n    \"commit\": {\n      \"id\": \"9be980d\",\n      \"time\": \"2025-03-12T03:40:38Z\"\n    }\n  },\n  \"build\": {\n    \"artifact\": \"komga\",\n    \"name\": \"komga\",\n    \"version\": \"1.21.2\",\n    \"group\": \"komga\"\n  },\n  \"java\": {\n    \"version\": \"23.0.2\",\n    \"vendor\": {\n      \"name\": \"Eclipse Adoptium\",\n      \"version\": \"Temurin-23.0.2+7\"\n    },\n    \"runtime\": {\n      \"name\": \"OpenJDK Runtime Environment\",\n      \"version\": \"23.0.2+7\"\n    },\n    \"jvm\": {\n      \"name\": \"OpenJDK 64-Bit Server VM\",\n      \"vendor\": \"Eclipse Adoptium\",\n      \"version\": \"23.0.2+7\"\n    }\n  },\n  \"os\": {\n    \"name\": \"Linux\",\n    \"version\": \"6.8.0-57-generic\",\n    \"arch\": \"amd64\"\n  }\n}"}}}}, "description": "OK"}}, "summary": "Get server information", "tags": ["Management"]}, "summary": "Get general information about the server"}, "/api/logout": {"get": {"description": "Invalidates the current session and clean up any remember-me authentication.", "operationId": "postLogout", "responses": {"204": {"description": "No Content"}}, "summary": "Logout", "tags": ["User session"]}, "post": {"description": "Invalidates the current session and clean up any remember-me authentication.", "operationId": "postLogout_1", "responses": {"204": {"description": "No Content"}}, "summary": "Logout", "tags": ["User session"]}, "summary": "Logout current session"}, "/api/v1/age-ratings": {"get": {"description": "Can be filtered by various criteria", "operationId": "getAgeRatings", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List age ratings", "tags": ["Referential metadata"]}}, "/api/v1/announcements": {"get": {"description": "Required role: **ADMIN**", "operationId": "getAnnouncements", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JsonFeedDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve announcements", "tags": ["Announcements"]}, "put": {"description": "Required role: **ADMIN**", "operationId": "markAnnouncementsRead", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark announcements as read", "tags": ["Announcements"]}}, "/api/v1/authors": {"get": {"deprecated": true, "description": "Use GET /api/v2/authors instead. Deprecated since 1.20.0.", "operationId": "getAuthorsDeprecated", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string", "default": ""}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "series_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List authors", "tags": ["Deprecated", "Referential metadata"]}}, "/api/v1/authors/names": {"get": {"operationId": "getAuthorsNames", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List authors' names", "tags": ["Referential metadata"]}}, "/api/v1/authors/roles": {"get": {"operationId": "getAuthorsRoles", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List authors' roles", "tags": ["Referential metadata"]}}, "/api/v1/books": {"get": {"deprecated": true, "description": "Use POST /api/v1/books/list instead. Deprecated since 1.19.0.", "operationId": "getAllBooksDeprecated", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "media_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNKNOWN", "ERROR", "READY", "UNSUPPORTED", "OUTDATED"]}}}, {"in": "query", "name": "read_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNREAD", "READ", "IN_PROGRESS"]}}}, {"in": "query", "name": "released_after", "required": false, "schema": {"type": "string", "format": "date"}}, {"in": "query", "name": "tag", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List books", "tags": ["Books", "Deprecated"]}}, "/api/v1/books/duplicates": {"get": {"description": "Return books that have the same file hash.\n\nRequired role: **ADMIN**", "operationId": "getBooksDuplicates", "parameters": [{"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List duplicate books", "tags": ["Books"]}}, "/api/v1/books/import": {"post": {"description": "Required role: **ADMIN**", "operationId": "importBooks", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookImportBatchDto"}}}, "required": true}, "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Import books", "tags": ["Import"]}}, "/api/v1/books/latest": {"get": {"description": "Return newly added or updated books.", "operationId": "getBooksLatest", "parameters": [{"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List latest books", "tags": ["Books"]}}, "/api/v1/books/list": {"post": {"operationId": "getBooks", "parameters": [{"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookSearch"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List books", "tags": ["Books"]}}, "/api/v1/books/metadata": {"patch": {"description": "Set a field to null to unset the metadata. You can omit fields you don't want to update.\n\nRequired role: **ADMIN**", "operationId": "updateBookMetadataByBatch", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/BookMetadataUpdateDto"}, "description": "A map of book IDs which values are the metadata fields to update. Set a field to null to unset the metadata. You can omit fields you don't want to update."}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update book metadata in bulk", "tags": ["Books"]}}, "/api/v1/books/ondeck": {"get": {"description": "Return first unread book of series with at least one book read and no books in progress.", "operationId": "getBooksOnDeck", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List books on deck", "tags": ["Books"]}}, "/api/v1/books/thumbnails": {"put": {"description": "Required role: **ADMIN**", "operationId": "booksRegenerateThumbnails", "parameters": [{"in": "query", "name": "for_bigger_result_only", "required": false, "schema": {"type": "boolean"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Regenerate books posters", "tags": ["Book Poster"]}}, "/api/v1/books/{bookId}": {"get": {"operationId": "getBookById", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get book details", "tags": ["Books"]}}, "/api/v1/books/{bookId}/analyze": {"post": {"description": "Required role: **ADMIN**", "operationId": "bookAnalyze", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Analyze book", "tags": ["Books"]}}, "/api/v1/books/{bookId}/file": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteBookFile", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete book file", "tags": ["Books"]}, "get": {"description": "Download the book file.\n\nRequired role: **FILE_DOWNLOAD**", "operationId": "downloadBookFile", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}, "application/octet-stream": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Download book file", "tags": ["Books"]}}, "/api/v1/books/{bookId}/file/*": {"get": {"description": "Download the book file.\n\nRequired role: **FILE_DOWNLOAD**", "operationId": "downloadBookFile_1", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}, "application/octet-stream": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Download book file", "tags": ["Books"]}}, "/api/v1/books/{bookId}/manifest": {"get": {"operationId": "getBookWebPubManifest", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/divina+json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}, "application/webpub+json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get book's WebPub manifest", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/manifest/divina": {"get": {"operationId": "getBookWebPubManifestDivina", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/divina+json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get book's WebPub manifest (DiViNa)", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/manifest/epub": {"get": {"operationId": "getBookWebPubManifestEpub", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}, "application/webpub+json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get book's WebPub manifest (Epub)", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/manifest/pdf": {"get": {"operationId": "getBookWebPubManifestPdf", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}, "application/webpub+json": {"schema": {"$ref": "#/components/schemas/WPPublicationDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get book's WebPub manifest (PDF)", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/metadata": {"patch": {"description": "Set a field to null to unset the metadata. You can omit fields you don't want to update.\n\nRequired role: **ADMIN**", "operationId": "updateBookMetadata", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookMetadataUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update book metadata", "tags": ["Books"]}}, "/api/v1/books/{bookId}/metadata/refresh": {"post": {"description": "Required role: **ADMIN**", "operationId": "bookRefreshMetadata", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Refresh book metadata", "tags": ["Books"]}}, "/api/v1/books/{bookId}/next": {"get": {"operationId": "getBookSiblingNext", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get next book in series", "tags": ["Books"]}}, "/api/v1/books/{bookId}/pages": {"get": {"operationId": "getBookPages", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PageDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List book pages", "tags": ["Book Pages"]}}, "/api/v1/books/{bookId}/pages/{pageNumber}": {"get": {"description": "Required role: **PAGE_STREAMING**", "operationId": "getBookPageByNumber", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "pageNumber", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"description": "Convert the image to the provided format.", "in": "query", "name": "convert", "required": false, "schema": {"type": "string", "enum": ["jpeg", "png"]}}, {"description": "If set to true, pages will start at index 0. If set to false, pages will start at index 1.", "in": "query", "name": "zero_based", "required": false, "schema": {"type": "boolean", "default": false}}, {"description": "Some very limited server driven content negotiation is handled. If a book is a PDF book, and the Accept header contains 'application/pdf' as a more specific type than other 'image/' types, a raw PDF page will be returned.", "in": "header", "name": "Accept", "required": false, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/MediaType"}}}, {"in": "query", "name": "contentNegotiation", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"image/*": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get book page image", "tags": ["Book Pages"]}}, "/api/v1/books/{bookId}/pages/{pageNumber}/raw": {"get": {"description": "Returns the book page in raw format, without content negotiation.\n\nRequired role: **PAGE_STREAMING**", "operationId": "getBookPageRawByNumber", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "pageNumber", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"content": {"*/*": {"schema": {"type": "string", "format": "byte"}}, "application/json": {"schema": {"type": "string", "format": "byte"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get raw book page", "tags": ["Book Pages"]}}, "/api/v1/books/{bookId}/pages/{pageNumber}/thumbnail": {"get": {"description": "The image is resized to 300px on the largest dimension.", "operationId": "getBookPageThumbnailByNumber", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "pageNumber", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get book page thumbnail", "tags": ["Book Pages"]}}, "/api/v1/books/{bookId}/positions": {"get": {"description": "The Positions API is a proposed standard for OPDS 2 and Readium. It is used by the Epub Reader.", "operationId": "getBookPositions", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/R2Positions"}}, "application/vnd.readium.position-list+json": {"schema": {"$ref": "#/components/schemas/R2Positions"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List book's positions", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/previous": {"get": {"operationId": "getBookSiblingPrevious", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get previous book in series", "tags": ["Books"]}}, "/api/v1/books/{bookId}/progression": {"get": {"description": "The Progression API is a proposed standard for OPDS 2 and Readium. It is used by the Epub Reader.", "operationId": "getBookProgression", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/R2Progression"}}, "application/vnd.readium.progression+json": {"schema": {"$ref": "#/components/schemas/R2Progression"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get book progression", "tags": ["WebPub Manifest"]}, "put": {"description": "The Progression API is a proposed standard for OPDS 2 and Readium. It is used by the Epub Reader.", "operationId": "updateBookProgression", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/R2Progression"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark book progression", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/read-progress": {"delete": {"description": "Mark book as unread", "operationId": "deleteBookReadProgress", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark book as unread", "tags": ["Books"]}, "patch": {"description": "Mark book as read and/or change page progress.", "operationId": "markBookReadProgress", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadProgressUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark book's read progress", "tags": ["Books"]}}, "/api/v1/books/{bookId}/readlists": {"get": {"operationId": "getReadListsByBookId", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReadListDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List book's readlists", "tags": ["Books"]}}, "/api/v1/books/{bookId}/resource/{resource}": {"get": {"description": "Return a resource from within an Epub book.", "operationId": "getBookEpubResource", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "resource", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"*/*": {"schema": {"type": "string", "format": "byte"}}, "application/json": {"schema": {"type": "string", "format": "byte"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "Get Epub resource", "tags": ["WebPub Manifest"]}}, "/api/v1/books/{bookId}/thumbnail": {"get": {"operationId": "getBookT<PERSON>bnail", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get book's poster image", "tags": ["Book Poster"]}}, "/api/v1/books/{bookId}/thumbnails": {"get": {"operationId": "getBookThumbnails", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ThumbnailBookDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List book posters", "tags": ["Book Poster"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "addUserUploadedBookThumbnail", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "selected", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThumbnailBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Add book poster", "tags": ["Book Poster"]}}, "/api/v1/books/{bookId}/thumbnails/{thumbnailId}": {"delete": {"description": "Only uploaded posters can be deleted.\n\nRequired role: **ADMIN**", "operationId": "deleteUserUploadedBookThumbnail", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete book poster", "tags": ["Book Poster"]}, "get": {"operationId": "getBookThumbnailById", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get book poster image", "tags": ["Book Poster"]}}, "/api/v1/books/{bookId}/thumbnails/{thumbnailId}/selected": {"put": {"description": "Required role: **ADMIN**", "operationId": "markBookThumbnailSelected", "parameters": [{"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark book poster as selected", "tags": ["Book Poster"]}}, "/api/v1/claim": {"get": {"description": "Check whether this server has already been claimed.", "operationId": "getClaimStatus", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimStatus"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "Retrieve claim status", "tags": ["<PERSON><PERSON><PERSON>"]}, "post": {"description": "Creates an admin user with the provided credentials.", "operationId": "claimServer", "parameters": [{"in": "header", "name": "X-Komga-Email", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "X-Komga-Password", "required": true, "schema": {"type": "string", "minLength": 1}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "Claim server", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/api/v1/client-settings/global": {"delete": {"description": "Setting key should be a valid lowercase namespace string like 'application.domain.key'\n\nRequired role: **ADMIN**", "operationId": "deleteGlobalSettings", "requestBody": {"content": {"application/json": {"example": ["application.key1", "application.key2"], "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete global settings", "tags": ["Client settings"]}, "patch": {"description": "Setting key should be a valid lowercase namespace string like 'application.domain.key'\n\nRequired role: **ADMIN**", "operationId": "saveGlobalSetting", "requestBody": {"content": {"application/json": {"example": {"application.key1": {"value": "a string value", "allowUnauthorized": true}, "application.key2": {"value": "{\"json\":\"object\"}", "allowUnauthorized": false}}, "schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClientSettingGlobalUpdateDto"}}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Save global settings", "tags": ["Client settings"]}}, "/api/v1/client-settings/global/list": {"get": {"description": "For unauthenticated users, only settings with 'allowUnauthorized=true' will be returned.", "operationId": "getGlobalSettings", "responses": {"200": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClientSettingDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "Retrieve global client settings", "tags": ["Client settings"]}}, "/api/v1/client-settings/user": {"delete": {"description": "Setting key should be a valid lowercase namespace string like 'application.domain.key'", "operationId": "deleteUserSettings", "requestBody": {"content": {"application/json": {"example": ["application.key1", "application.key2"], "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete user settings", "tags": ["Client settings"]}, "patch": {"description": "Setting key should be a valid lowercase namespace string like 'application.domain.key'", "operationId": "saveUserSetting", "requestBody": {"content": {"application/json": {"example": {"application.key1": {"value": "a string value"}, "application.key2": {"value": "{\"json\":\"object\"}"}}, "schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClientSettingUserUpdateDto"}}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Save user settings", "tags": ["Client settings"]}}, "/api/v1/client-settings/user/list": {"get": {"operationId": "getUserSettings", "responses": {"200": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClientSettingDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve user client settings", "tags": ["Client settings"]}}, "/api/v1/collections": {"get": {"operationId": "getCollections", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCollectionDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List collections", "tags": ["Collections"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "createCollection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionCreationDto"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Create collection", "tags": ["Collections"]}}, "/api/v1/collections/{id}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteCollectionById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete collection", "tags": ["Collections"]}, "get": {"operationId": "getCollectionById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get collection details", "tags": ["Collections"]}, "patch": {"description": "Required role: **ADMIN**", "operationId": "updateCollectionById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update collection", "tags": ["Collections"]}}, "/api/v1/collections/{id}/series": {"get": {"operationId": "getSeriesByCollectionId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ENDED", "ONGOING", "ABANDONED", "HIATUS"]}}}, {"in": "query", "name": "read_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNREAD", "READ", "IN_PROGRESS"]}}}, {"in": "query", "name": "publisher", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "language", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "genre", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "tag", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "age_rating", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "release_year", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "complete", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Author criteria in the format: name,role. Multiple author criteria are supported.", "in": "query", "name": "author", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List collection's series", "tags": ["Collection Series"]}}, "/api/v1/collections/{id}/thumbnail": {"get": {"operationId": "getCollectionThumbnail", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get collection's poster image", "tags": ["Collection Poster"]}}, "/api/v1/collections/{id}/thumbnails": {"get": {"operationId": "getCollectionThumbnails", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ThumbnailSeriesCollectionDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List collection's posters", "tags": ["Collection Poster"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "addUserUploadedCollectionThumbnail", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "selected", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThumbnailSeriesCollectionDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Add collection poster", "tags": ["Collection Poster"]}}, "/api/v1/collections/{id}/thumbnails/{thumbnailId}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteUserUploadedCollectionThumbnail", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete collection poster", "tags": ["Collection Poster"]}, "get": {"operationId": "getCollectionThumbnailById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get collection poster image", "tags": ["Collection Poster"]}}, "/api/v1/collections/{id}/thumbnails/{thumbnailId}/selected": {"put": {"description": "Required role: **ADMIN**", "operationId": "markCollectionThumbnailSelected", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark collection poster as selected", "tags": ["Collection Poster"]}}, "/api/v1/filesystem": {"post": {"description": "List folders and files from the host server's file system. If no request body is passed then the root directories are returned.\n\nRequired role: **ADMIN**", "operationId": "getDirectoryListing", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DirectoryRequestDto"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DirectoryListingDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Directory listing", "tags": ["File system"]}}, "/api/v1/fonts/families": {"get": {"description": "List all available font families.", "operationId": "getFonts", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List font families", "tags": ["Fonts"]}}, "/api/v1/fonts/resource/{fontFamily}/css": {"get": {"description": "Download a CSS file with the @font-face block for the font family. This is used by the Epub Reader to change fonts.", "operationId": "getFontFamilyAsCss", "parameters": [{"in": "path", "name": "fontFamily", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "text/css": {"schema": {"type": "string", "format": "binary"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "Download CSS file", "tags": ["Fonts"]}}, "/api/v1/fonts/resource/{fontFamily}/{fontFile}": {"get": {"operationId": "getFontFile", "parameters": [{"in": "path", "name": "fontFamily", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "fontFile", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "Download font file", "tags": ["Fonts"]}}, "/api/v1/genres": {"get": {"description": "Can be filtered by various criteria", "operationId": "getGenres", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List genres", "tags": ["Referential metadata"]}}, "/api/v1/history": {"get": {"description": "Required role: **ADMIN**", "operationId": "getHistoricalEvents", "parameters": [{"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageHistoricalEventDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List historical events", "tags": ["History"]}}, "/api/v1/languages": {"get": {"description": "Can be filtered by various criteria", "operationId": "getLanguages", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List languages", "tags": ["Referential metadata"]}}, "/api/v1/libraries": {"get": {"description": "The libraries are filtered based on the current user's permissions", "operationId": "getLibraries", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LibraryDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List all libraries", "tags": ["Libraries"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "addLibrary", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LibraryCreationDto"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LibraryDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Create a library", "tags": ["Libraries"]}}, "/api/v1/libraries/{libraryId}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteLibraryById", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete a library", "tags": ["Libraries"]}, "get": {"operationId": "getLibraryById", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LibraryDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get details for a single library", "tags": ["Libraries"]}, "patch": {"description": "You can omit fields you don't want to update\n\nRequired role: **ADMIN**", "operationId": "updateLibraryById", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LibraryUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update a library", "tags": ["Libraries"]}, "put": {"deprecated": true, "description": "Use PATCH /api/v1/libraries/{libraryId} instead. Deprecated since 1.3.0.\n\nRequired role: **ADMIN**", "operationId": "updateLibraryByIdDeprecated", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LibraryUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update a library", "tags": ["Deprecated", "Libraries"]}}, "/api/v1/libraries/{libraryId}/analyze": {"post": {"description": "Required role: **ADMIN**", "operationId": "libraryAnalyze", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Analyze a library", "tags": ["Libraries"]}}, "/api/v1/libraries/{libraryId}/empty-trash": {"post": {"description": "Required role: **ADMIN**", "operationId": "libraryEmptyTrash", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Empty trash for a library", "tags": ["Libraries"]}}, "/api/v1/libraries/{libraryId}/metadata/refresh": {"post": {"description": "Required role: **ADMIN**", "operationId": "libraryRefreshMetadata", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Refresh metadata for a library", "tags": ["Libraries"]}}, "/api/v1/libraries/{libraryId}/scan": {"post": {"description": "Required role: **ADMIN**", "operationId": "libraryScan", "parameters": [{"in": "path", "name": "libraryId", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "deep", "required": false, "schema": {"type": "boolean"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Scan a library", "tags": ["Libraries"]}}, "/api/v1/login/set-cookie": {"get": {"description": "Forcefully return <PERSON><PERSON><PERSON><PERSON> header, even if the session is contained in the X-Auth-Token header.", "operationId": "convertHeaderSessionToCookie", "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Set cookie", "tags": ["User session"]}}, "/api/v1/oauth2/providers": {"get": {"operationId": "getOAuth2Providers", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OAuth2ClientDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "security": [], "summary": "List registered OAuth2 providers", "tags": ["OAuth2"]}}, "/api/v1/page-hashes": {"get": {"description": "Required role: **ADMIN**", "operationId": "getKnownPageHashes", "parameters": [{"in": "query", "name": "action", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["DELETE_AUTO", "DELETE_MANUAL", "IGNORE"]}}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagePageHashKnownDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List known duplicates", "tags": ["Duplicate Pages"]}, "put": {"description": "Required role: **ADMIN**", "operationId": "createOrUpdateKnownPageHash", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageHashCreationDto"}}}, "required": true}, "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark duplicate page as known", "tags": ["Duplicate Pages"]}}, "/api/v1/page-hashes/unknown": {"get": {"description": "Required role: **ADMIN**", "operationId": "getUnknownPageHashes", "parameters": [{"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagePageHashUnknownDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List unknown duplicates", "tags": ["Duplicate Pages"]}}, "/api/v1/page-hashes/unknown/{pageHash}/thumbnail": {"get": {"description": "Required role: **ADMIN**", "operationId": "getUnknownPageHashThumbnail", "parameters": [{"in": "path", "name": "pageHash", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "resize", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get unknown duplicate image thumbnail", "tags": ["Duplicate Pages"]}}, "/api/v1/page-hashes/{pageHash}": {"get": {"description": "Required role: **ADMIN**", "operationId": "getPageHashMatches", "parameters": [{"in": "path", "name": "pageHash", "required": true, "schema": {"type": "string"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagePageHashMatchDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List duplicate matches", "tags": ["Duplicate Pages"]}}, "/api/v1/page-hashes/{pageHash}/delete-all": {"post": {"description": "Required role: **ADMIN**", "operationId": "deleteDuplicatePagesByPageHash", "parameters": [{"in": "path", "name": "pageHash", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete all duplicate pages by hash", "tags": ["Duplicate Pages"]}}, "/api/v1/page-hashes/{pageHash}/delete-match": {"post": {"description": "Required role: **ADMIN**", "operationId": "deleteSingleMatchByPageHash", "parameters": [{"in": "path", "name": "pageHash", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageHashMatchDto"}}}, "required": true}, "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete specific duplicate page", "tags": ["Duplicate Pages"]}}, "/api/v1/page-hashes/{pageHash}/thumbnail": {"get": {"description": "Required role: **ADMIN**", "operationId": "getKnownPageHashThumbnail", "parameters": [{"in": "path", "name": "pageHash", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get known duplicate image thumbnail", "tags": ["Duplicate Pages"]}}, "/api/v1/publishers": {"get": {"description": "Can be filtered by various criteria", "operationId": "getPublishers", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List publishers", "tags": ["Referential metadata"]}}, "/api/v1/readlists": {"get": {"operationId": "getReadLists", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageReadListDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List readlists", "tags": ["Readlists"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "createReadList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadListCreationDto"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadListDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Create readlist", "tags": ["Readlists"]}}, "/api/v1/readlists/match/comicrack": {"post": {"description": "Required role: **ADMIN**", "operationId": "matchComicRackList", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadListRequestMatchDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Match ComicRack list", "tags": ["ComicRack"]}}, "/api/v1/readlists/{id}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteReadListById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete readlist", "tags": ["Readlists"]}, "get": {"operationId": "getReadListById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadListDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get readlist details", "tags": ["Readlists"]}, "patch": {"description": "Required role: **ADMIN**", "operationId": "updateReadListById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadListUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update readlist", "tags": ["Readlists"]}}, "/api/v1/readlists/{id}/books": {"get": {"operationId": "getBooksByReadListId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "read_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNREAD", "READ", "IN_PROGRESS"]}}}, {"in": "query", "name": "tag", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "media_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNKNOWN", "ERROR", "READY", "UNSUPPORTED", "OUTDATED"]}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Author criteria in the format: name,role. Multiple author criteria are supported.", "in": "query", "name": "author", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List readlist's books", "tags": ["Readlist Books"]}}, "/api/v1/readlists/{id}/books/{bookId}/next": {"get": {"operationId": "getBookSiblingNextInReadList", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get next book in readlist", "tags": ["Readlist Books"]}}, "/api/v1/readlists/{id}/books/{bookId}/previous": {"get": {"operationId": "getBookSiblingPreviousInReadList", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "bookId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get previous book in readlist", "tags": ["Readlist Books"]}}, "/api/v1/readlists/{id}/file": {"get": {"description": "Download the whole readlist as a ZIP file.\n\nRequired role: **FILE_DOWNLOAD**", "operationId": "downloadReadListAsZip", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}, "application/octet-stream": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Download readlist", "tags": ["Readlists"]}}, "/api/v1/readlists/{id}/read-progress/tachiyomi": {"get": {"description": "Mihon specific, due to how read progress is handled in Mihon.", "operationId": "getMihonReadProgressByReadListId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TachiyomiReadProgressDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get readlist read progress (Mihon)", "tags": ["<PERSON><PERSON>"]}, "put": {"description": "Mihon specific, due to how read progress is handled in Mihon.", "operationId": "updateMihonReadProgressByReadListId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TachiyomiReadProgressUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update readlist read progress (Mihon)", "tags": ["<PERSON><PERSON>"]}}, "/api/v1/readlists/{id}/thumbnail": {"get": {"operationId": "getReadListThumbnail", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get readlist's poster image", "tags": ["Readlist Poster"]}}, "/api/v1/readlists/{id}/thumbnails": {"get": {"operationId": "getReadListThumbnails", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ThumbnailReadListDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List readlist's posters", "tags": ["Readlist Poster"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "addUserUploadedReadListThumbnail", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "selected", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThumbnailReadListDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Add readlist poster", "tags": ["Readlist Poster"]}}, "/api/v1/readlists/{id}/thumbnails/{thumbnailId}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteUserUploadedReadListThumbnail", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete readlist poster", "tags": ["Readlist Poster"]}, "get": {"operationId": "getReadListThumbnailById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get readlist poster image", "tags": ["Readlist Poster"]}}, "/api/v1/readlists/{id}/thumbnails/{thumbnailId}/selected": {"put": {"description": "Required role: **ADMIN**", "operationId": "markReadListThumbnailSelected", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "<PERSON> readlist poster as selected", "tags": ["Readlist Poster"]}}, "/api/v1/releases": {"get": {"description": "Required role: **ADMIN**", "operationId": "getReleases", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReleaseDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List releases", "tags": ["Releases"]}}, "/api/v1/series": {"get": {"deprecated": true, "description": "Use POST /api/v1/series/list instead. Deprecated since 1.19.0.", "operationId": "getSeriesDeprecated", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ENDED", "ONGOING", "ABANDONED", "HIATUS"]}}}, {"in": "query", "name": "read_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNREAD", "READ", "IN_PROGRESS"]}}}, {"in": "query", "name": "publisher", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "language", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "genre", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "tag", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "age_rating", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "release_year", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "sharing_label", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "complete", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "oneshot", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Search by regex criteria, in the form: regex,field. Supported fields are TITLE and TITLE_SORT.", "in": "query", "name": "search_regex", "schema": {"type": "string"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Author criteria in the format: name,role. Multiple author criteria are supported.", "in": "query", "name": "author", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series", "tags": ["Series", "Deprecated"]}}, "/api/v1/series/alphabetical-groups": {"get": {"deprecated": true, "description": "Use POST /api/v1/series/list/alphabetical-groups instead. Deprecated since 1.19.0.", "operationId": "getSeriesAlphabeticalGroupsDeprecated", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ENDED", "ONGOING", "ABANDONED", "HIATUS"]}}}, {"in": "query", "name": "read_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNREAD", "READ", "IN_PROGRESS"]}}}, {"in": "query", "name": "publisher", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "language", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "genre", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "tag", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "age_rating", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "release_year", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "sharing_label", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "complete", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "oneshot", "required": false, "schema": {"type": "boolean"}}, {"description": "Search by regex criteria, in the form: regex,field. Supported fields are TITLE and TITLE_SORT.", "in": "query", "name": "search_regex", "schema": {"type": "string"}}, {"description": "Author criteria in the format: name,role. Multiple author criteria are supported.", "in": "query", "name": "author", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GroupCountDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series groups", "tags": ["Series", "Deprecated"]}}, "/api/v1/series/latest": {"get": {"description": "Return recently added or updated series.", "operationId": "getSeriesLatest", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "oneshot", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List latest series", "tags": ["Series"]}}, "/api/v1/series/list": {"post": {"operationId": "getSeries", "parameters": [{"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeriesSearch"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series", "tags": ["Series"]}}, "/api/v1/series/list/alphabetical-groups": {"post": {"description": "List series grouped by the first character of their sort title.", "operationId": "getSeriesAlphabeticalGroups", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeriesSearch"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GroupCountDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series groups", "tags": ["Series"]}}, "/api/v1/series/new": {"get": {"description": "Return newly added series.", "operationId": "getSeriesNew", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "oneshot", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List new series", "tags": ["Series"]}}, "/api/v1/series/release-dates": {"get": {"description": "Can be filtered by various criteria", "operationId": "getSeriesReleaseDates", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series release dates", "tags": ["Referential metadata"]}}, "/api/v1/series/updated": {"get": {"description": "Return recently updated series, but not newly added ones.", "operationId": "getSeriesUpdated", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "oneshot", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List updated series", "tags": ["Series"]}}, "/api/v1/series/{seriesId}": {"get": {"operationId": "getSeriesById", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get series details", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/analyze": {"post": {"description": "Required role: **ADMIN**", "operationId": "seriesAnalyze", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Analyze series", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/books": {"get": {"deprecated": true, "description": "Use POST /api/v1/books/list instead. Deprecated since 1.19.0.", "operationId": "getBooksBySeriesId", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "media_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNKNOWN", "ERROR", "READY", "UNSUPPORTED", "OUTDATED"]}}}, {"in": "query", "name": "read_status", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UNREAD", "READ", "IN_PROGRESS"]}}}, {"in": "query", "name": "tag", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "query", "name": "deleted", "required": false, "schema": {"type": "boolean"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}, {"description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Author criteria in the format: name,role. Multiple author criteria are supported.", "in": "query", "name": "author", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series' books", "tags": ["Series", "Deprecated"]}}, "/api/v1/series/{seriesId}/collections": {"get": {"operationId": "getCollectionsBySeriesId", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series' collections", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/file": {"delete": {"description": "Delete all of the series' books files on disk.\n\nRequired role: **ADMIN**", "operationId": "deleteSeriesFile", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete series files", "tags": ["Series"]}, "get": {"description": "Download the whole series as a ZIP file.\n\nRequired role: **FILE_DOWNLOAD**", "operationId": "downloadSeriesAsZip", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}, "application/octet-stream": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Download series", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/metadata": {"patch": {"description": "Required role: **ADMIN**", "operationId": "updateSeriesMetadata", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeriesMetadataUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update series metadata", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/metadata/refresh": {"post": {"description": "Required role: **ADMIN**", "operationId": "seriesRefreshMetadata", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Refresh series metadata", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/read-progress": {"delete": {"description": "Mark all book for series as unread", "operationId": "markSeriesAsUnread", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark series as unread", "tags": ["Series"]}, "post": {"description": "Mark all book for series as read", "operationId": "markSeriesAsRead", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark series as read", "tags": ["Series"]}}, "/api/v1/series/{seriesId}/thumbnail": {"get": {"operationId": "getSeriesThumbnail", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get series' poster image", "tags": ["Series Poster"]}}, "/api/v1/series/{seriesId}/thumbnails": {"get": {"operationId": "getSeriesThumbnails", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ThumbnailSeriesDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series posters", "tags": ["Series Poster"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "addUserUploadedSeriesThumbnail", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "selected", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThumbnailSeriesDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Add series poster", "tags": ["Series Poster"]}}, "/api/v1/series/{seriesId}/thumbnails/{thumbnailId}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteUserUploadedSeriesThumbnail", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete series poster", "tags": ["Series Poster"]}, "get": {"operationId": "getSeriesThumbnailById", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}, "default": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}}, "description": "default response"}}, "summary": "Get series poster image", "tags": ["Series Poster"]}}, "/api/v1/series/{seriesId}/thumbnails/{thumbnailId}/selected": {"put": {"description": "Required role: **ADMIN**", "operationId": "markSeriesThumbnailSelected", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "thumbnailId", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Mark series poster as selected", "tags": ["Series Poster"]}}, "/api/v1/settings": {"get": {"description": "Required role: **ADMIN**", "operationId": "getServerSettings", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve server settings", "tags": ["Server settings"]}, "patch": {"description": "You can omit fields you don't want to update\n\nRequired role: **ADMIN**", "operationId": "updateServerSettings", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update server settings", "tags": ["Server settings"]}}, "/api/v1/sharing-labels": {"get": {"description": "Can be filtered by various criteria", "operationId": "getSharingLabels", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List sharing labels", "tags": ["Referential metadata"]}}, "/api/v1/syncpoints/me": {"delete": {"description": "If an API Key ID is passed, deletes only the sync points associated with that API Key. Deleting sync points will allow a Kobo to sync from scratch upon the next sync.", "operationId": "deleteSyncPointsForCurrentUser", "parameters": [{"in": "query", "name": "key_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete all sync points", "tags": ["Sync points"]}}, "/api/v1/tags": {"get": {"description": "Can be filtered by various criteria", "operationId": "getTags", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List tags", "tags": ["Referential metadata"]}}, "/api/v1/tags/book": {"get": {"description": "Can be filtered by various criteria", "operationId": "getBookTags", "parameters": [{"in": "query", "name": "series_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "readlist_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List book tags", "tags": ["Referential metadata"]}}, "/api/v1/tags/series": {"get": {"description": "Can be filtered by various criteria", "operationId": "getSeriesTags", "parameters": [{"in": "query", "name": "library_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List series tags", "tags": ["Referential metadata"]}}, "/api/v1/tasks": {"delete": {"description": "Cancel all tasks queued\n\nRequired role: **ADMIN**", "operationId": "emptyTaskQueue", "responses": {"200": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Clear task queue", "tags": ["Tasks"]}}, "/api/v1/transient-books": {"post": {"description": "<PERSON><PERSON> provided folder for transient books.\n\nRequired role: **ADMIN**", "operationId": "scanTransientBooks", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanRequestDto"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransientBookDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Scan folder for transient books", "tags": ["Import"]}}, "/api/v1/transient-books/{id}/analyze": {"post": {"description": "Required role: **ADMIN**", "operationId": "analyzeTransientBook", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransientBookDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Analyze transient book", "tags": ["Import"]}}, "/api/v1/transient-books/{id}/pages/{pageNumber}": {"get": {"description": "Required role: **ADMIN**", "operationId": "getPageByTransientBookId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "path", "name": "pageNumber", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"content": {"*/*": {"schema": {"type": "string", "format": "byte"}}, "application/json": {"schema": {"type": "string", "format": "byte"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get transient book page", "tags": ["Import"]}}, "/api/v2/authors": {"get": {"description": "Can be filtered by various criteria", "operationId": "getAuthors", "parameters": [{"in": "query", "name": "search", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "role", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "library_id", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"in": "query", "name": "collection_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "series_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "readlist_id", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer"}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAuthorDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List authors", "tags": ["Referential metadata"]}}, "/api/v2/series/{seriesId}/read-progress/tachiyomi": {"get": {"description": "Mihon specific, due to how read progress is handled in Mihon.", "operationId": "getMihonReadProgressBySeriesId", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TachiyomiReadProgressV2Dto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Get series read progress (<PERSON>hon)", "tags": ["<PERSON><PERSON>"]}, "put": {"description": "Mihon specific, due to how read progress is handled in Mihon.", "operationId": "updateMihonReadProgressBySeriesId", "parameters": [{"in": "path", "name": "seriesId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TachiyomiReadProgressUpdateV2Dto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update series read progress (Mihon)", "tags": ["<PERSON><PERSON>"]}}, "/api/v2/users": {"get": {"description": "Required role: **ADMIN**", "operationId": "getUsers", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "List users", "tags": ["Users"]}, "post": {"description": "Required role: **ADMIN**", "operationId": "addUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreationDto"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}, "description": "Created"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Create user", "tags": ["Users"]}}, "/api/v2/users/authentication-activity": {"get": {"description": "Required role: **ADMIN**", "operationId": "getAuthenticationActivity", "parameters": [{"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer", "default": 0}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer", "default": 20}}, {"description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAuthenticationActivityDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve authentication activity", "tags": ["Users"]}}, "/api/v2/users/me": {"get": {"operationId": "getCurrentUser", "parameters": [{"in": "query", "name": "remember-me", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve current user", "tags": ["Current user"]}}, "/api/v2/users/me/api-keys": {"get": {"operationId": "getApiKeysForCurrentUser", "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ApiKeyDto"}}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve API keys", "tags": ["API Keys"]}, "post": {"operationId": "createApiKeyForCurrentUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyRequestDto"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Create API key", "tags": ["API Keys"]}}, "/api/v2/users/me/api-keys/{keyId}": {"delete": {"operationId": "deleteApiKeyByKeyId", "parameters": [{"in": "path", "name": "keyId", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete API key", "tags": ["API Keys"]}}, "/api/v2/users/me/authentication-activity": {"get": {"operationId": "getAuthenticationActivityForCurrentUser", "parameters": [{"in": "query", "name": "unpaged", "required": false, "schema": {"type": "boolean"}}, {"description": "Zero-based page index (0..N)", "in": "query", "name": "page", "schema": {"type": "integer", "default": 0}}, {"description": "The size of the page to be returned", "in": "query", "name": "size", "schema": {"type": "integer", "default": 20}}, {"description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAuthenticationActivityDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve authentication activity for the current user", "tags": ["Current user"]}}, "/api/v2/users/me/password": {"patch": {"operationId": "updatePasswordForCurrentUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update current user's password", "tags": ["Current user"]}}, "/api/v2/users/{id}": {"delete": {"description": "Required role: **ADMIN**", "operationId": "deleteUserById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Delete user", "tags": ["Users"]}, "patch": {"description": "Required role: **ADMIN**", "operationId": "updateUserById", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update user", "tags": ["Users"]}}, "/api/v2/users/{id}/authentication-activity/latest": {"get": {"description": "Required role: **ADMIN**", "operationId": "getLatestAuthenticationActivityByUserId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "apikey_id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationActivityDto"}}}, "description": "OK"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Retrieve latest authentication activity for a user", "tags": ["Users"]}}, "/api/v2/users/{id}/password": {"patch": {"description": "Required role: **ADMIN**", "operationId": "updatePasswordByUserId", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordUpdateDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}, "description": "Bad Request"}}, "summary": "Update user's password", "tags": ["Users"]}}}, "components": {"schemas": {"After": {"allOf": [{"$ref": "#/components/schemas/Date"}, {"type": "object", "properties": {"dateTime": {"type": "string", "format": "date-time"}}}], "required": ["dateTime"]}, "AgeRating": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"ageRating": {"oneOf": [{"$ref": "#/components/schemas/GreaterThan"}, {"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}, {"$ref": "#/components/schemas/IsNotNullT"}, {"$ref": "#/components/schemas/IsNullT"}, {"$ref": "#/components/schemas/LessThan"}]}}}], "required": ["ageRating"]}, "AgeRestrictionDto": {"type": "object", "properties": {"age": {"type": "integer", "format": "int32"}, "restriction": {"type": "string", "enum": ["ALLOW_ONLY", "EXCLUDE"]}}, "required": ["age", "restriction"]}, "AgeRestrictionUpdateDto": {"type": "object", "properties": {"age": {"type": "integer", "format": "int32"}, "restriction": {"type": "string", "enum": ["ALLOW_ONLY", "EXCLUDE", "NONE"]}}, "required": ["age", "restriction"]}, "AllOfBook": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"allOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/AllOfBook"}, {"$ref": "#/components/schemas/AnyOfBook"}, {"$ref": "#/components/schemas/Author"}, {"$ref": "#/components/schemas/Deleted"}, {"$ref": "#/components/schemas/LibraryId"}, {"$ref": "#/components/schemas/MediaProfile"}, {"$ref": "#/components/schemas/MediaStatus"}, {"$ref": "#/components/schemas/NumberSort"}, {"$ref": "#/components/schemas/OneShot"}, {"$ref": "#/components/schemas/Poster"}, {"$ref": "#/components/schemas/ReadListId"}, {"$ref": "#/components/schemas/ReadStatus"}, {"$ref": "#/components/schemas/ReleaseDate"}, {"$ref": "#/components/schemas/SeriesId"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/Title"}]}}}}], "required": ["allOf"]}, "AllOfSeries": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"allOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/AgeRating"}, {"$ref": "#/components/schemas/AllOfSeries"}, {"$ref": "#/components/schemas/AnyOfSeries"}, {"$ref": "#/components/schemas/Author"}, {"$ref": "#/components/schemas/CollectionId"}, {"$ref": "#/components/schemas/Complete"}, {"$ref": "#/components/schemas/Deleted"}, {"$ref": "#/components/schemas/Genre"}, {"$ref": "#/components/schemas/Language"}, {"$ref": "#/components/schemas/LibraryId"}, {"$ref": "#/components/schemas/OneShot"}, {"$ref": "#/components/schemas/Publisher"}, {"$ref": "#/components/schemas/ReadStatus"}, {"$ref": "#/components/schemas/ReleaseDate"}, {"$ref": "#/components/schemas/SeriesStatus"}, {"$ref": "#/components/schemas/SharingLabel"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/Title"}, {"$ref": "#/components/schemas/TitleSort"}]}}}}], "required": ["allOf"]}, "AlternateTitleDto": {"type": "object", "properties": {"label": {"type": "string"}, "title": {"type": "string"}}, "required": ["label", "title"]}, "AlternateTitleUpdateDto": {"type": "object", "properties": {"label": {"type": "string", "minLength": 1}, "title": {"type": "string", "minLength": 1}}, "required": ["label", "title"]}, "AnyOfBook": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"anyOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/AllOfBook"}, {"$ref": "#/components/schemas/AnyOfBook"}, {"$ref": "#/components/schemas/Author"}, {"$ref": "#/components/schemas/Deleted"}, {"$ref": "#/components/schemas/LibraryId"}, {"$ref": "#/components/schemas/MediaProfile"}, {"$ref": "#/components/schemas/MediaStatus"}, {"$ref": "#/components/schemas/NumberSort"}, {"$ref": "#/components/schemas/OneShot"}, {"$ref": "#/components/schemas/Poster"}, {"$ref": "#/components/schemas/ReadListId"}, {"$ref": "#/components/schemas/ReadStatus"}, {"$ref": "#/components/schemas/ReleaseDate"}, {"$ref": "#/components/schemas/SeriesId"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/Title"}]}}}}], "required": ["anyOf"]}, "AnyOfSeries": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"anyOf": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/AgeRating"}, {"$ref": "#/components/schemas/AllOfSeries"}, {"$ref": "#/components/schemas/AnyOfSeries"}, {"$ref": "#/components/schemas/Author"}, {"$ref": "#/components/schemas/CollectionId"}, {"$ref": "#/components/schemas/Complete"}, {"$ref": "#/components/schemas/Deleted"}, {"$ref": "#/components/schemas/Genre"}, {"$ref": "#/components/schemas/Language"}, {"$ref": "#/components/schemas/LibraryId"}, {"$ref": "#/components/schemas/OneShot"}, {"$ref": "#/components/schemas/Publisher"}, {"$ref": "#/components/schemas/ReadStatus"}, {"$ref": "#/components/schemas/ReleaseDate"}, {"$ref": "#/components/schemas/SeriesStatus"}, {"$ref": "#/components/schemas/SharingLabel"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/Title"}, {"$ref": "#/components/schemas/TitleSort"}]}}}}], "required": ["anyOf"]}, "ApiKeyDto": {"type": "object", "properties": {"comment": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "key": {"type": "string"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "userId": {"type": "string"}}, "required": ["comment", "createdDate", "id", "key", "lastModifiedDate", "userId"]}, "ApiKeyRequestDto": {"type": "object", "properties": {"comment": {"type": "string", "minLength": 1}}, "required": ["comment"]}, "AuthenticationActivityDto": {"type": "object", "properties": {"apiKeyComment": {"type": "string"}, "apiKeyId": {"type": "string"}, "dateTime": {"type": "string", "format": "date-time"}, "email": {"type": "string"}, "error": {"type": "string"}, "ip": {"type": "string"}, "source": {"type": "string"}, "success": {"type": "boolean"}, "userAgent": {"type": "string"}, "userId": {"type": "string"}}, "required": ["dateTime", "success"]}, "Author": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"author": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["author"]}, "AuthorDto": {"type": "object", "properties": {"name": {"type": "string"}, "role": {"type": "string"}}, "required": ["name", "role"]}, "AuthorUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "role": {"type": "string", "minLength": 1}}, "required": ["name", "role"]}, "Before": {"allOf": [{"$ref": "#/components/schemas/Date"}, {"type": "object", "properties": {"dateTime": {"type": "string", "format": "date-time"}}}], "required": ["dateTime"]}, "BeginsWith": {"allOf": [{"$ref": "#/components/schemas/StringOp"}, {"type": "object", "properties": {"value": {"type": "string"}}}], "required": ["value"]}, "Book": {}, "BookDto": {"type": "object", "properties": {"created": {"type": "string", "format": "date-time"}, "deleted": {"type": "boolean"}, "fileHash": {"type": "string"}, "fileLastModified": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "lastModified": {"type": "string", "format": "date-time"}, "libraryId": {"type": "string"}, "media": {"$ref": "#/components/schemas/MediaDto"}, "metadata": {"$ref": "#/components/schemas/BookMetadataDto"}, "name": {"type": "string"}, "number": {"type": "integer", "format": "int32"}, "oneshot": {"type": "boolean"}, "readProgress": {"$ref": "#/components/schemas/ReadProgressDto"}, "seriesId": {"type": "string"}, "seriesTitle": {"type": "string"}, "size": {"type": "string"}, "sizeBytes": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}, "required": ["created", "deleted", "fileHash", "fileLastModified", "id", "lastModified", "libraryId", "media", "metadata", "name", "number", "oneshot", "seriesId", "seriesTitle", "size", "sizeBytes", "url"]}, "BookImportBatchDto": {"type": "object", "properties": {"books": {"type": "array", "items": {"$ref": "#/components/schemas/BookImportDto"}}, "copyMode": {"type": "string", "enum": ["MOVE", "COPY", "HARDLINK"]}}, "required": ["books", "copyMode"]}, "BookImportDto": {"type": "object", "properties": {"destinationName": {"type": "string"}, "seriesId": {"type": "string"}, "sourceFile": {"type": "string"}, "upgradeBookId": {"type": "string"}}, "required": ["seriesId", "sourceFile"]}, "BookMetadataAggregationDto": {"type": "object", "properties": {"authors": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorDto"}}, "created": {"type": "string", "format": "date-time"}, "lastModified": {"type": "string", "format": "date-time"}, "releaseDate": {"type": "string", "format": "date"}, "summary": {"type": "string"}, "summaryNumber": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["authors", "created", "lastModified", "summary", "summaryNumber", "tags"]}, "BookMetadataDto": {"type": "object", "properties": {"authors": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorDto"}}, "authorsLock": {"type": "boolean"}, "created": {"type": "string", "format": "date-time"}, "isbn": {"type": "string"}, "isbnLock": {"type": "boolean"}, "lastModified": {"type": "string", "format": "date-time"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/WebLinkDto"}}, "linksLock": {"type": "boolean"}, "number": {"type": "string"}, "numberLock": {"type": "boolean"}, "numberSort": {"type": "number", "format": "float"}, "numberSortLock": {"type": "boolean"}, "releaseDate": {"type": "string", "format": "date"}, "releaseDateLock": {"type": "boolean"}, "summary": {"type": "string"}, "summaryLock": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "tagsLock": {"type": "boolean"}, "title": {"type": "string"}, "titleLock": {"type": "boolean"}}, "required": ["authors", "authorsLock", "created", "isbn", "isbnLock", "lastModified", "links", "linksLock", "number", "numberLock", "numberSort", "numberSortLock", "releaseDateLock", "summary", "summaryLock", "tags", "tagsLock", "title", "titleLock"]}, "BookMetadataUpdateDto": {"type": "object", "description": "Metadata fields to update. Set a field to null to unset the metadata. You can omit fields you don't want to update.", "properties": {"authors": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorUpdateDto"}}, "authorsLock": {"type": "boolean"}, "isbn": {"type": "string"}, "isbnLock": {"type": "boolean"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/WebLinkUpdateDto"}}, "linksLock": {"type": "boolean"}, "number": {"type": "string"}, "numberLock": {"type": "boolean"}, "numberSort": {"type": "number", "format": "float"}, "numberSortLock": {"type": "boolean"}, "releaseDate": {"type": "string", "format": "date"}, "releaseDateLock": {"type": "boolean"}, "summary": {"type": "string"}, "summaryLock": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "tagsLock": {"type": "boolean"}, "title": {"type": "string"}, "titleLock": {"type": "boolean"}}}, "BookSearch": {"type": "object", "properties": {"condition": {"oneOf": [{"$ref": "#/components/schemas/AllOfBook"}, {"$ref": "#/components/schemas/AnyOfBook"}, {"$ref": "#/components/schemas/Author"}, {"$ref": "#/components/schemas/Deleted"}, {"$ref": "#/components/schemas/LibraryId"}, {"$ref": "#/components/schemas/MediaProfile"}, {"$ref": "#/components/schemas/MediaStatus"}, {"$ref": "#/components/schemas/NumberSort"}, {"$ref": "#/components/schemas/OneShot"}, {"$ref": "#/components/schemas/Poster"}, {"$ref": "#/components/schemas/ReadListId"}, {"$ref": "#/components/schemas/ReadStatus"}, {"$ref": "#/components/schemas/ReleaseDate"}, {"$ref": "#/components/schemas/SeriesId"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/Title"}]}, "fullTextSearch": {"type": "string"}}}, "Boolean": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "ClaimStatus": {"type": "object", "properties": {"isClaimed": {"type": "boolean"}}, "required": ["isClaimed"]}, "ClientSettingDto": {"type": "object", "properties": {"allowUnauthorized": {"type": "boolean"}, "value": {"type": "string"}}, "required": ["value"]}, "ClientSettingGlobalUpdateDto": {"type": "object", "properties": {"allowUnauthorized": {"type": "boolean"}, "value": {"type": "string", "minLength": 1}}, "required": ["allowUnauthorized", "value"]}, "ClientSettingUserUpdateDto": {"type": "object", "properties": {"value": {"type": "string", "minLength": 1}}, "required": ["value"]}, "CollectionCreationDto": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "ordered": {"type": "boolean"}, "seriesIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["name", "ordered", "seriesIds"]}, "CollectionDto": {"type": "object", "properties": {"createdDate": {"type": "string", "format": "date-time"}, "filtered": {"type": "boolean"}, "id": {"type": "string"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "ordered": {"type": "boolean"}, "seriesIds": {"type": "array", "items": {"type": "string"}}}, "required": ["createdDate", "filtered", "id", "lastModifiedDate", "name", "ordered", "seriesIds"]}, "CollectionId": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"collectionId": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["collectionId"]}, "CollectionUpdateDto": {"type": "object", "properties": {"name": {"type": "string"}, "ordered": {"type": "boolean"}, "seriesIds": {"type": "array", "items": {"type": "string"}}}}, "Complete": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"complete": {"oneOf": [{"$ref": "#/components/schemas/IsFalse"}, {"$ref": "#/components/schemas/IsTrue"}]}}}], "required": ["complete"]}, "Contains": {"allOf": [{"$ref": "#/components/schemas/StringOp"}, {"type": "object", "properties": {"value": {"type": "string"}}}], "required": ["value"]}, "Date": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "Deleted": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"deleted": {"oneOf": [{"$ref": "#/components/schemas/IsFalse"}, {"$ref": "#/components/schemas/IsTrue"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["deleted"]}, "DirectoryListingDto": {"type": "object", "properties": {"directories": {"type": "array", "items": {"$ref": "#/components/schemas/PathDto"}}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/PathDto"}}, "parent": {"type": "string"}}, "required": ["directories", "files"]}, "DirectoryRequestDto": {"type": "object", "properties": {"path": {"type": "string"}, "showFiles": {"type": "boolean"}}, "required": ["path", "showFiles"]}, "DoesNotBeginWith": {"allOf": [{"$ref": "#/components/schemas/StringOp"}, {"type": "object", "properties": {"value": {"type": "string"}}}], "required": ["value"]}, "DoesNotContain": {"allOf": [{"$ref": "#/components/schemas/StringOp"}, {"type": "object", "properties": {"value": {"type": "string"}}}], "required": ["value"]}, "DoesNotEndWith": {"allOf": [{"$ref": "#/components/schemas/StringOp"}, {"type": "object", "properties": {"value": {"type": "string"}}}], "required": ["value"]}, "EndsWith": {"allOf": [{"$ref": "#/components/schemas/StringOp"}, {"type": "object", "properties": {"value": {"type": "string"}}}], "required": ["value"]}, "EqualityAuthorMatch": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "EqualityMediaProfile": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "EqualityNullableString": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "EqualityPosterMatch": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "EqualityReadStatus": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "EqualityStatus": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "EqualityString": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "Genre": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"genre": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}, {"$ref": "#/components/schemas/IsNotNullT"}, {"$ref": "#/components/schemas/IsNullT"}]}}}], "required": ["genre"]}, "GreaterThan": {"allOf": [{"$ref": "#/components/schemas/NumericNullableInteger"}, {"type": "object", "properties": {"value": {}}}, {"$ref": "#/components/schemas/NumericFloat"}], "required": ["value"]}, "GroupCountDto": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "group": {"type": "string"}}, "required": ["count", "group"]}, "HistoricalEventDto": {"type": "object", "properties": {"bookId": {"type": "string"}, "id": {"type": "string"}, "properties": {"type": "object", "additionalProperties": {"type": "string"}}, "seriesId": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "type": {"type": "string"}}, "required": ["id", "properties", "timestamp", "type"]}, "Is": {"allOf": [{"$ref": "#/components/schemas/NumericNullableInteger"}, {"type": "object", "properties": {"value": {}}}, {"$ref": "#/components/schemas/EqualityAuthorMatch"}, {"$ref": "#/components/schemas/EqualityString"}, {"$ref": "#/components/schemas/EqualityNullableString"}, {"$ref": "#/components/schemas/EqualityReadStatus"}, {"$ref": "#/components/schemas/EqualityStatus"}, {"$ref": "#/components/schemas/StringOp"}, {"$ref": "#/components/schemas/EqualityMediaProfile"}, {"$ref": "#/components/schemas/NumericFloat"}, {"$ref": "#/components/schemas/EqualityPosterMatch"}], "required": ["value"]}, "IsFalse": {"allOf": [{"$ref": "#/components/schemas/Bo<PERSON>an"}]}, "IsInTheLast": {"allOf": [{"$ref": "#/components/schemas/Date"}, {"type": "object", "properties": {"duration": {"type": "string"}}}], "required": ["duration"]}, "IsNot": {"allOf": [{"$ref": "#/components/schemas/NumericNullableInteger"}, {"type": "object", "properties": {"value": {}}}, {"$ref": "#/components/schemas/EqualityAuthorMatch"}, {"$ref": "#/components/schemas/EqualityString"}, {"$ref": "#/components/schemas/EqualityNullableString"}, {"$ref": "#/components/schemas/EqualityReadStatus"}, {"$ref": "#/components/schemas/EqualityStatus"}, {"$ref": "#/components/schemas/StringOp"}, {"$ref": "#/components/schemas/EqualityMediaProfile"}, {"$ref": "#/components/schemas/NumericFloat"}, {"$ref": "#/components/schemas/EqualityPosterMatch"}], "required": ["value"]}, "IsNotInTheLast": {"allOf": [{"$ref": "#/components/schemas/Date"}, {"type": "object", "properties": {"duration": {"type": "string"}}}], "required": ["duration"]}, "IsNotNull": {"allOf": [{"$ref": "#/components/schemas/Date"}]}, "IsNotNullT": {"allOf": [{"$ref": "#/components/schemas/NumericNullableInteger"}, {"$ref": "#/components/schemas/EqualityNullableString"}]}, "IsNull": {"allOf": [{"$ref": "#/components/schemas/Date"}]}, "IsNullT": {"allOf": [{"$ref": "#/components/schemas/NumericNullableInteger"}, {"$ref": "#/components/schemas/EqualityNullableString"}]}, "IsTrue": {"allOf": [{"$ref": "#/components/schemas/Bo<PERSON>an"}]}, "ItemDto": {"type": "object", "properties": {"_komga": {"$ref": "#/components/schemas/KomgaExtensionDto"}, "author": {"$ref": "#/components/schemas/AuthorDto"}, "content_html": {"type": "string"}, "date_modified": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "title": {"type": "string"}, "url": {"type": "string"}}, "required": ["id", "tags"]}, "JsonFeedDto": {"type": "object", "properties": {"description": {"type": "string"}, "home_page_url": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ItemDto"}}, "title": {"type": "string"}, "version": {"type": "string"}}, "required": ["items", "title", "version"]}, "KomgaExtensionDto": {"type": "object", "properties": {"read": {"type": "boolean"}}, "required": ["read"]}, "Language": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"language": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["language"]}, "LessThan": {"allOf": [{"$ref": "#/components/schemas/NumericNullableInteger"}, {"type": "object", "properties": {"value": {}}}, {"$ref": "#/components/schemas/NumericFloat"}], "required": ["value"]}, "LibraryCreationDto": {"type": "object", "properties": {"analyzeDimensions": {"type": "boolean"}, "convertToCbz": {"type": "boolean"}, "emptyTrashAfterScan": {"type": "boolean"}, "hashFiles": {"type": "boolean"}, "hashKoreader": {"type": "boolean"}, "hashPages": {"type": "boolean"}, "importBarcodeIsbn": {"type": "boolean"}, "importComicInfoBook": {"type": "boolean"}, "importComicInfoCollection": {"type": "boolean"}, "importComicInfoReadList": {"type": "boolean"}, "importComicInfoSeries": {"type": "boolean"}, "importComicInfoSeriesAppendVolume": {"type": "boolean"}, "importEpubBook": {"type": "boolean"}, "importEpubSeries": {"type": "boolean"}, "importLocalArtwork": {"type": "boolean"}, "importMylarSeries": {"type": "boolean"}, "name": {"type": "string", "minLength": 1}, "oneshotsDirectory": {"type": "string"}, "repairExtensions": {"type": "boolean"}, "root": {"type": "string", "minLength": 1}, "scanCbx": {"type": "boolean"}, "scanDirectoryExclusions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "scanEpub": {"type": "boolean"}, "scanForceModifiedTime": {"type": "boolean"}, "scanInterval": {"type": "string", "enum": ["DISABLED", "HOURLY", "EVERY_6H", "EVERY_12H", "DAILY", "WEEKLY"]}, "scanOnStartup": {"type": "boolean"}, "scanPdf": {"type": "boolean"}, "seriesCover": {"type": "string", "enum": ["FIRST", "FIRST_UNREAD_OR_FIRST", "FIRST_UNREAD_OR_LAST", "LAST"]}}, "required": ["analyzeDimensions", "convertToCbz", "emptyTrashAfterScan", "hashFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hashPages", "importBarcodeIsbn", "importComicInfoBook", "importComicInfoCollection", "importComicInfoReadList", "importComicInfoSeries", "importComicInfoSeriesAppendVolume", "importEpubBook", "importEpubSeries", "importLocalArtwork", "importMylarSeries", "name", "repairExtensions", "root", "scanCbx", "scanDirectoryExclusions", "scanEpub", "scanForceModifiedTime", "scanInterval", "scanOnStartup", "scanPdf", "seriesCover"]}, "LibraryDto": {"type": "object", "properties": {"analyzeDimensions": {"type": "boolean"}, "convertToCbz": {"type": "boolean"}, "emptyTrashAfterScan": {"type": "boolean"}, "hashFiles": {"type": "boolean"}, "hashKoreader": {"type": "boolean"}, "hashPages": {"type": "boolean"}, "id": {"type": "string"}, "importBarcodeIsbn": {"type": "boolean"}, "importComicInfoBook": {"type": "boolean"}, "importComicInfoCollection": {"type": "boolean"}, "importComicInfoReadList": {"type": "boolean"}, "importComicInfoSeries": {"type": "boolean"}, "importComicInfoSeriesAppendVolume": {"type": "boolean"}, "importEpubBook": {"type": "boolean"}, "importEpubSeries": {"type": "boolean"}, "importLocalArtwork": {"type": "boolean"}, "importMylarSeries": {"type": "boolean"}, "name": {"type": "string"}, "oneshotsDirectory": {"type": "string"}, "repairExtensions": {"type": "boolean"}, "root": {"type": "string"}, "scanCbx": {"type": "boolean"}, "scanDirectoryExclusions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "scanEpub": {"type": "boolean"}, "scanForceModifiedTime": {"type": "boolean"}, "scanInterval": {"type": "string", "enum": ["DISABLED", "HOURLY", "EVERY_6H", "EVERY_12H", "DAILY", "WEEKLY"]}, "scanOnStartup": {"type": "boolean"}, "scanPdf": {"type": "boolean"}, "seriesCover": {"type": "string", "enum": ["FIRST", "FIRST_UNREAD_OR_FIRST", "FIRST_UNREAD_OR_LAST", "LAST"]}, "unavailable": {"type": "boolean"}}, "required": ["analyzeDimensions", "convertToCbz", "emptyTrashAfterScan", "hashFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hashPages", "id", "importBarcodeIsbn", "importComicInfoBook", "importComicInfoCollection", "importComicInfoReadList", "importComicInfoSeries", "importComicInfoSeriesAppendVolume", "importEpubBook", "importEpubSeries", "importLocalArtwork", "importMylarSeries", "name", "repairExtensions", "root", "scanCbx", "scanDirectoryExclusions", "scanEpub", "scanForceModifiedTime", "scanInterval", "scanOnStartup", "scanPdf", "seriesCover", "unavailable"]}, "LibraryId": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"libraryId": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["libraryId"]}, "LibraryUpdateDto": {"type": "object", "description": "Fields to update. You can omit fields you don't want to update.", "properties": {"analyzeDimensions": {"type": "boolean"}, "convertToCbz": {"type": "boolean"}, "emptyTrashAfterScan": {"type": "boolean"}, "hashFiles": {"type": "boolean"}, "hashKoreader": {"type": "boolean"}, "hashPages": {"type": "boolean"}, "importBarcodeIsbn": {"type": "boolean"}, "importComicInfoBook": {"type": "boolean"}, "importComicInfoCollection": {"type": "boolean"}, "importComicInfoReadList": {"type": "boolean"}, "importComicInfoSeries": {"type": "boolean"}, "importComicInfoSeriesAppendVolume": {"type": "boolean"}, "importEpubBook": {"type": "boolean"}, "importEpubSeries": {"type": "boolean"}, "importLocalArtwork": {"type": "boolean"}, "importMylarSeries": {"type": "boolean"}, "name": {"type": "string"}, "oneshotsDirectory": {"type": "string"}, "repairExtensions": {"type": "boolean"}, "root": {"type": "string"}, "scanCbx": {"type": "boolean"}, "scanDirectoryExclusions": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "scanEpub": {"type": "boolean"}, "scanForceModifiedTime": {"type": "boolean"}, "scanInterval": {"type": "string", "enum": ["DISABLED", "HOURLY", "EVERY_6H", "EVERY_12H", "DAILY", "WEEKLY"]}, "scanOnStartup": {"type": "boolean"}, "scanPdf": {"type": "boolean"}, "seriesCover": {"type": "string", "enum": ["FIRST", "FIRST_UNREAD_OR_FIRST", "FIRST_UNREAD_OR_LAST", "LAST"]}}}, "Location": {"type": "object", "properties": {"fragments": {"type": "array", "items": {"type": "string"}}, "position": {"type": "integer", "format": "int32"}, "progression": {"type": "number", "format": "float"}, "totalProgression": {"type": "number", "format": "float"}}, "required": ["fragments"]}, "MediaDto": {"type": "object", "properties": {"comment": {"type": "string"}, "epubDivinaCompatible": {"type": "boolean"}, "epubIsKepub": {"type": "boolean"}, "mediaProfile": {"type": "string"}, "mediaType": {"type": "string"}, "pagesCount": {"type": "integer", "format": "int32"}, "status": {"type": "string"}}, "required": ["comment", "epubDivinaCompatible", "epubIsKepub", "mediaProfile", "mediaType", "pagesCount", "status"]}, "MediaProfile": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"mediaProfile": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["mediaProfile"]}, "MediaStatus": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"mediaStatus": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["mediaStatus"]}, "MediaType": {"type": "object", "properties": {"charset": {"type": "string"}, "concrete": {"type": "boolean"}, "parameters": {"type": "object", "additionalProperties": {"type": "string"}}, "qualityValue": {"type": "number", "format": "double"}, "subtype": {"type": "string"}, "subtypeSuffix": {"type": "string"}, "type": {"type": "string"}, "wildcardSubtype": {"type": "boolean"}, "wildcardType": {"type": "boolean"}}}, "NumberSort": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"numberSort": {"oneOf": [{"$ref": "#/components/schemas/GreaterThan"}, {"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}, {"$ref": "#/components/schemas/LessThan"}]}}}], "required": ["numberSort"]}, "NumericFloat": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "NumericNullableInteger": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "OAuth2ClientDto": {"type": "object", "properties": {"name": {"type": "string"}, "registrationId": {"type": "string"}}, "required": ["name", "registrationId"]}, "OneShot": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"oneShot": {"oneOf": [{"$ref": "#/components/schemas/IsFalse"}, {"$ref": "#/components/schemas/IsTrue"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["oneShot"]}, "PageAuthenticationActivityDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AuthenticationActivityDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageAuthorDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageBookDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/BookDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageCollectionDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageDto": {"type": "object", "properties": {"fileName": {"type": "string"}, "height": {"type": "integer", "format": "int32"}, "mediaType": {"type": "string"}, "number": {"type": "integer", "format": "int32"}, "size": {"type": "string"}, "sizeBytes": {"type": "integer", "format": "int64"}, "width": {"type": "integer", "format": "int32"}}, "required": ["fileName", "mediaType", "number", "size"]}, "PageHashCreationDto": {"type": "object", "properties": {"action": {"type": "string", "enum": ["DELETE_AUTO", "DELETE_MANUAL", "IGNORE"]}, "hash": {"type": "string", "minLength": 1}, "size": {"type": "integer", "format": "int64"}}, "required": ["action", "hash"]}, "PageHashKnownDto": {"type": "object", "properties": {"action": {"type": "string", "enum": ["DELETE_AUTO", "DELETE_MANUAL", "IGNORE"]}, "created": {"type": "string", "format": "date-time"}, "deleteCount": {"type": "integer", "format": "int32"}, "hash": {"type": "string"}, "lastModified": {"type": "string", "format": "date-time"}, "matchCount": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int64"}}, "required": ["action", "created", "deleteCount", "hash", "lastModified", "matchCount"]}, "PageHashMatchDto": {"type": "object", "properties": {"bookId": {"type": "string"}, "fileName": {"type": "string"}, "fileSize": {"type": "integer", "format": "int64"}, "mediaType": {"type": "string"}, "pageNumber": {"type": "integer", "format": "int32"}, "url": {"type": "string"}}, "required": ["bookId", "fileName", "fileSize", "mediaType", "pageNumber", "url"]}, "PageHashUnknownDto": {"type": "object", "properties": {"hash": {"type": "string"}, "matchCount": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int64"}}, "required": ["hash", "matchCount"]}, "PageHistoricalEventDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/HistoricalEventDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PagePageHashKnownDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PageHashKnownDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PagePageHashMatchDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PageHashMatchDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PagePageHashUnknownDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PageHashUnknownDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageReadListDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/ReadListDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageSeriesDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/SeriesDto"}}, "empty": {"type": "boolean"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "size": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "PageableObject": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "unpaged": {"type": "boolean"}}}, "PasswordUpdateDto": {"type": "object", "properties": {"password": {"type": "string", "minLength": 1}}, "required": ["password"]}, "PathDto": {"type": "object", "properties": {"name": {"type": "string"}, "path": {"type": "string"}, "type": {"type": "string"}}, "required": ["name", "path", "type"]}, "Poster": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"poster": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["poster"]}, "Publisher": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"publisher": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["publisher"]}, "R2Device": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "R2Locator": {"type": "object", "properties": {"href": {"type": "string"}, "koboSpan": {"type": "string"}, "locations": {"$ref": "#/components/schemas/Location"}, "text": {"$ref": "#/components/schemas/Text"}, "title": {"type": "string"}, "type": {"type": "string"}}, "required": ["href", "type"]}, "R2Positions": {"type": "object", "properties": {"positions": {"type": "array", "items": {"$ref": "#/components/schemas/R2Locator"}}, "total": {"type": "integer", "format": "int32"}}, "required": ["positions", "total"]}, "R2Progression": {"type": "object", "properties": {"device": {"$ref": "#/components/schemas/R2Device"}, "locator": {"$ref": "#/components/schemas/R2Locator"}, "modified": {"type": "string", "format": "date-time"}}, "required": ["device", "locator", "modified"]}, "ReadListCreationDto": {"type": "object", "properties": {"bookIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "name": {"type": "string", "minLength": 1}, "ordered": {"type": "boolean"}, "summary": {"type": "string"}}, "required": ["bookIds", "name", "ordered", "summary"]}, "ReadListDto": {"type": "object", "properties": {"bookIds": {"type": "array", "items": {"type": "string"}}, "createdDate": {"type": "string", "format": "date-time"}, "filtered": {"type": "boolean"}, "id": {"type": "string"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "ordered": {"type": "boolean"}, "summary": {"type": "string"}}, "required": ["bookIds", "createdDate", "filtered", "id", "lastModifiedDate", "name", "ordered", "summary"]}, "ReadListId": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"readListId": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["readListId"]}, "ReadListMatchDto": {"type": "object", "properties": {"errorCode": {"type": "string"}, "name": {"type": "string"}}, "required": ["errorCode", "name"]}, "ReadListRequestBookDto": {"type": "object", "properties": {"number": {"type": "string"}, "series": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["number", "series"]}, "ReadListRequestBookMatchBookDto": {"type": "object", "properties": {"bookId": {"type": "string"}, "number": {"type": "string"}, "title": {"type": "string"}}, "required": ["bookId", "number", "title"]}, "ReadListRequestBookMatchDto": {"type": "object", "properties": {"books": {"type": "array", "items": {"$ref": "#/components/schemas/ReadListRequestBookMatchBookDto"}}, "series": {"$ref": "#/components/schemas/ReadListRequestBookMatchSeriesDto"}}, "required": ["books", "series"]}, "ReadListRequestBookMatchSeriesDto": {"type": "object", "properties": {"releaseDate": {"type": "string", "format": "date"}, "seriesId": {"type": "string"}, "title": {"type": "string"}}, "required": ["seriesId", "title"]}, "ReadListRequestBookMatchesDto": {"type": "object", "properties": {"matches": {"type": "array", "items": {"$ref": "#/components/schemas/ReadListRequestBookMatchDto"}}, "request": {"$ref": "#/components/schemas/ReadListRequestBookDto"}}, "required": ["matches", "request"]}, "ReadListRequestMatchDto": {"type": "object", "properties": {"errorCode": {"type": "string"}, "readListMatch": {"$ref": "#/components/schemas/ReadListMatchDto"}, "requests": {"type": "array", "items": {"$ref": "#/components/schemas/ReadListRequestBookMatchesDto"}}}, "required": ["errorCode", "readListMatch", "requests"]}, "ReadListUpdateDto": {"type": "object", "properties": {"bookIds": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "ordered": {"type": "boolean"}, "summary": {"type": "string"}}}, "ReadProgressDto": {"type": "object", "properties": {"completed": {"type": "boolean"}, "created": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "deviceName": {"type": "string"}, "lastModified": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "readDate": {"type": "string", "format": "date-time"}}, "required": ["completed", "created", "deviceId", "deviceName", "lastModified", "page", "readDate"]}, "ReadProgressUpdateDto": {"type": "object", "description": "page can be omitted if completed is set to true. completed can be omitted, and will be set accordingly depending on the page passed and the total number of pages in the book.", "properties": {"completed": {"type": "boolean"}, "page": {"type": "integer", "format": "int32"}}}, "ReadStatus": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"readStatus": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["readStatus"]}, "ReleaseDate": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"releaseDate": {"oneOf": [{"$ref": "#/components/schemas/After"}, {"$ref": "#/components/schemas/Before"}, {"$ref": "#/components/schemas/IsInTheLast"}, {"$ref": "#/components/schemas/IsNotInTheLast"}, {"$ref": "#/components/schemas/IsNotNull"}, {"$ref": "#/components/schemas/IsNull"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["releaseDate"]}, "ReleaseDto": {"type": "object", "properties": {"description": {"type": "string"}, "latest": {"type": "boolean"}, "preRelease": {"type": "boolean"}, "releaseDate": {"type": "string", "format": "date-time"}, "url": {"type": "string"}, "version": {"type": "string"}}, "required": ["description", "latest", "preRelease", "releaseDate", "url", "version"]}, "ScanRequestDto": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"]}, "Series": {}, "SeriesDto": {"type": "object", "properties": {"booksCount": {"type": "integer", "format": "int32"}, "booksInProgressCount": {"type": "integer", "format": "int32"}, "booksMetadata": {"$ref": "#/components/schemas/BookMetadataAggregationDto"}, "booksReadCount": {"type": "integer", "format": "int32"}, "booksUnreadCount": {"type": "integer", "format": "int32"}, "created": {"type": "string", "format": "date-time"}, "deleted": {"type": "boolean"}, "fileLastModified": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "lastModified": {"type": "string", "format": "date-time"}, "libraryId": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/SeriesMetadataDto"}, "name": {"type": "string"}, "oneshot": {"type": "boolean"}, "url": {"type": "string"}}, "required": ["booksCount", "booksInProgressCount", "booksMetadata", "booksReadCount", "booksUnreadCount", "created", "deleted", "fileLastModified", "id", "lastModified", "libraryId", "metadata", "name", "oneshot", "url"]}, "SeriesId": {"allOf": [{"$ref": "#/components/schemas/Book"}, {"type": "object", "properties": {"seriesId": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["seriesId"]}, "SeriesMetadataDto": {"type": "object", "properties": {"ageRating": {"type": "integer", "format": "int32"}, "ageRatingLock": {"type": "boolean"}, "alternateTitles": {"type": "array", "items": {"$ref": "#/components/schemas/AlternateTitleDto"}}, "alternateTitlesLock": {"type": "boolean"}, "created": {"type": "string", "format": "date-time"}, "genres": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "genresLock": {"type": "boolean"}, "language": {"type": "string"}, "languageLock": {"type": "boolean"}, "lastModified": {"type": "string", "format": "date-time"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/WebLinkDto"}}, "linksLock": {"type": "boolean"}, "publisher": {"type": "string"}, "publisherLock": {"type": "boolean"}, "readingDirection": {"type": "string"}, "readingDirectionLock": {"type": "boolean"}, "sharingLabels": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "sharingLabelsLock": {"type": "boolean"}, "status": {"type": "string"}, "statusLock": {"type": "boolean"}, "summary": {"type": "string"}, "summaryLock": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "tagsLock": {"type": "boolean"}, "title": {"type": "string"}, "titleLock": {"type": "boolean"}, "titleSort": {"type": "string"}, "titleSortLock": {"type": "boolean"}, "totalBookCount": {"type": "integer", "format": "int32"}, "totalBookCountLock": {"type": "boolean"}}, "required": ["ageRatingLock", "alternateTitles", "alternateTitlesLock", "created", "genres", "genresLock", "language", "languageLock", "lastModified", "links", "linksLock", "publisher", "publisherLock", "readingDirection", "readingDirectionLock", "sharingLabels", "sharingLabelsLock", "status", "statusLock", "summary", "summaryLock", "tags", "tagsLock", "title", "titleLock", "titleSort", "titleSortLock", "totalBookCountLock"]}, "SeriesMetadataUpdateDto": {"type": "object", "description": "Metadata fields to update. Set a field to null to unset the metadata. You can omit fields you don't want to update.", "properties": {"ageRating": {"type": "integer", "format": "int32"}, "ageRatingLock": {"type": "boolean"}, "alternateTitles": {"type": "array", "items": {"$ref": "#/components/schemas/AlternateTitleUpdateDto"}}, "alternateTitlesLock": {"type": "boolean"}, "genres": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "genresLock": {"type": "boolean"}, "language": {"type": "string"}, "languageLock": {"type": "boolean"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/WebLinkUpdateDto"}}, "linksLock": {"type": "boolean"}, "publisher": {"type": "string"}, "publisherLock": {"type": "boolean"}, "readingDirection": {"type": "string", "enum": ["LEFT_TO_RIGHT", "RIGHT_TO_LEFT", "VERTICAL", "WEBTOON"]}, "readingDirectionLock": {"type": "boolean"}, "sharingLabels": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "sharingLabelsLock": {"type": "boolean"}, "status": {"type": "string", "enum": ["ENDED", "ONGOING", "ABANDONED", "HIATUS"]}, "statusLock": {"type": "boolean"}, "summary": {"type": "string"}, "summaryLock": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "tagsLock": {"type": "boolean"}, "title": {"type": "string"}, "titleLock": {"type": "boolean"}, "titleSort": {"type": "string"}, "titleSortLock": {"type": "boolean"}, "totalBookCount": {"type": "integer", "format": "int32"}, "totalBookCountLock": {"type": "boolean"}}}, "SeriesSearch": {"type": "object", "properties": {"condition": {"oneOf": [{"$ref": "#/components/schemas/AgeRating"}, {"$ref": "#/components/schemas/AllOfSeries"}, {"$ref": "#/components/schemas/AnyOfSeries"}, {"$ref": "#/components/schemas/Author"}, {"$ref": "#/components/schemas/CollectionId"}, {"$ref": "#/components/schemas/Complete"}, {"$ref": "#/components/schemas/Deleted"}, {"$ref": "#/components/schemas/Genre"}, {"$ref": "#/components/schemas/Language"}, {"$ref": "#/components/schemas/LibraryId"}, {"$ref": "#/components/schemas/OneShot"}, {"$ref": "#/components/schemas/Publisher"}, {"$ref": "#/components/schemas/ReadStatus"}, {"$ref": "#/components/schemas/ReleaseDate"}, {"$ref": "#/components/schemas/SeriesStatus"}, {"$ref": "#/components/schemas/SharingLabel"}, {"$ref": "#/components/schemas/Tag"}, {"$ref": "#/components/schemas/Title"}, {"$ref": "#/components/schemas/TitleSort"}]}, "fullTextSearch": {"type": "string"}}}, "SeriesStatus": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"seriesStatus": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["seriesStatus"]}, "SettingMultiSourceInteger": {"type": "object", "properties": {"configurationSource": {"type": "integer", "format": "int32"}, "databaseSource": {"type": "integer", "format": "int32"}, "effectiveValue": {"type": "integer", "format": "int32"}}, "required": ["configurationSource", "databaseSource", "effectiveValue"]}, "SettingMultiSourceString": {"type": "object", "properties": {"configurationSource": {"type": "string"}, "databaseSource": {"type": "string"}, "effectiveValue": {"type": "string"}}, "required": ["configurationSource", "databaseSource", "effectiveValue"]}, "SettingsDto": {"type": "object", "properties": {"deleteEmptyCollections": {"type": "boolean"}, "deleteEmptyReadLists": {"type": "boolean"}, "kepubifyPath": {"$ref": "#/components/schemas/SettingMultiSourceString"}, "koboPort": {"type": "integer", "format": "int32"}, "koboProxy": {"type": "boolean"}, "rememberMeDurationDays": {"type": "integer", "format": "int64"}, "serverContextPath": {"$ref": "#/components/schemas/SettingMultiSourceString"}, "serverPort": {"$ref": "#/components/schemas/SettingMultiSourceInteger"}, "taskPoolSize": {"type": "integer", "format": "int32"}, "thumbnailSize": {"type": "string", "enum": ["DEFAULT", "MEDIUM", "LARGE", "XLARGE"]}}, "required": ["deleteEmptyCollections", "deleteEmptyReadLists", "kepubifyPath", "koboProxy", "rememberMeDurationDays", "serverContextPath", "serverPort", "taskPoolSize", "thumbnailSize"]}, "SettingsUpdateDto": {"type": "object", "description": "Fields to update. You can omit fields you don't want to update.", "properties": {"deleteEmptyCollections": {"type": "boolean"}, "deleteEmptyReadLists": {"type": "boolean"}, "kepubifyPath": {"type": "string", "deprecated": true, "description": "Will be removed in a future version"}, "koboPort": {"type": "integer", "format": "int32", "maximum": 65535}, "koboProxy": {"type": "boolean"}, "rememberMeDurationDays": {"type": "integer", "format": "int64"}, "renewRememberMeKey": {"type": "boolean"}, "serverContextPath": {"type": "string", "pattern": "^/[\\w-/]*[a-zA-Z0-9]$"}, "serverPort": {"type": "integer", "format": "int32", "maximum": 65535}, "taskPoolSize": {"type": "integer", "format": "int32"}, "thumbnailSize": {"type": "string", "enum": ["DEFAULT", "MEDIUM", "LARGE", "XLARGE"]}}}, "SharedLibrariesUpdateDto": {"type": "object", "properties": {"all": {"type": "boolean"}, "libraryIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["all", "libraryIds"]}, "SharingLabel": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"sharingLabel": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}, {"$ref": "#/components/schemas/IsNotNullT"}, {"$ref": "#/components/schemas/IsNullT"}]}}}], "required": ["sharingLabel"]}, "SortObject": {"type": "object", "properties": {"empty": {"type": "boolean"}, "sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}}}, "StreamingResponseBody": {}, "StringOp": {"discriminator": {"propertyName": "operator"}, "properties": {"operator": {"type": "string"}}, "required": ["operator"]}, "TachiyomiReadProgressDto": {"type": "object", "properties": {"booksCount": {"type": "integer", "format": "int32"}, "booksInProgressCount": {"type": "integer", "format": "int32"}, "booksReadCount": {"type": "integer", "format": "int32"}, "booksUnreadCount": {"type": "integer", "format": "int32"}, "lastReadContinuousIndex": {"type": "integer", "format": "int32"}}, "required": ["booksCount", "booksInProgressCount", "booksReadCount", "booksUnreadCount", "lastReadContinuousIndex"]}, "TachiyomiReadProgressUpdateDto": {"type": "object", "properties": {"lastBookRead": {"type": "integer", "format": "int32"}}, "required": ["lastBookRead"]}, "TachiyomiReadProgressUpdateV2Dto": {"type": "object", "properties": {"lastBookNumberSortRead": {"type": "number", "format": "float"}}, "required": ["lastBookNumberSortRead"]}, "TachiyomiReadProgressV2Dto": {"type": "object", "properties": {"booksCount": {"type": "integer", "format": "int32"}, "booksInProgressCount": {"type": "integer", "format": "int32"}, "booksReadCount": {"type": "integer", "format": "int32"}, "booksUnreadCount": {"type": "integer", "format": "int32"}, "lastReadContinuousNumberSort": {"type": "number", "format": "float"}, "maxNumberSort": {"type": "number", "format": "float"}}, "required": ["booksCount", "booksInProgressCount", "booksReadCount", "booksUnreadCount", "lastReadContinuousNumberSort", "maxNumberSort"]}, "Tag": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"tag": {"oneOf": [{"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}, {"$ref": "#/components/schemas/IsNotNullT"}, {"$ref": "#/components/schemas/IsNullT"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["tag"]}, "Text": {"type": "object", "properties": {"after": {"type": "string"}, "before": {"type": "string"}, "highlight": {"type": "string"}}}, "ThumbnailBookDto": {"type": "object", "properties": {"bookId": {"type": "string"}, "fileSize": {"type": "integer", "format": "int64"}, "height": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "mediaType": {"type": "string"}, "selected": {"type": "boolean"}, "type": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}, "required": ["bookId", "fileSize", "height", "id", "mediaType", "selected", "type", "width"]}, "ThumbnailReadListDto": {"type": "object", "properties": {"fileSize": {"type": "integer", "format": "int64"}, "height": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "mediaType": {"type": "string"}, "readListId": {"type": "string"}, "selected": {"type": "boolean"}, "type": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}, "required": ["fileSize", "height", "id", "mediaType", "readListId", "selected", "type", "width"]}, "ThumbnailSeriesCollectionDto": {"type": "object", "properties": {"collectionId": {"type": "string"}, "fileSize": {"type": "integer", "format": "int64"}, "height": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "mediaType": {"type": "string"}, "selected": {"type": "boolean"}, "type": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}, "required": ["collectionId", "fileSize", "height", "id", "mediaType", "selected", "type", "width"]}, "ThumbnailSeriesDto": {"type": "object", "properties": {"fileSize": {"type": "integer", "format": "int64"}, "height": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "mediaType": {"type": "string"}, "selected": {"type": "boolean"}, "seriesId": {"type": "string"}, "type": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}, "required": ["fileSize", "height", "id", "mediaType", "selected", "seriesId", "type", "width"]}, "Title": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"title": {"oneOf": [{"$ref": "#/components/schemas/BeginsWith"}, {"$ref": "#/components/schemas/Contains"}, {"$ref": "#/components/schemas/DoesNotBeginWith"}, {"$ref": "#/components/schemas/DoesNotContain"}, {"$ref": "#/components/schemas/DoesNotEndWith"}, {"$ref": "#/components/schemas/EndsWith"}, {"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}, {"$ref": "#/components/schemas/Book"}], "required": ["title"]}, "TitleSort": {"allOf": [{"$ref": "#/components/schemas/Series"}, {"type": "object", "properties": {"titleSort": {"oneOf": [{"$ref": "#/components/schemas/BeginsWith"}, {"$ref": "#/components/schemas/Contains"}, {"$ref": "#/components/schemas/DoesNotBeginWith"}, {"$ref": "#/components/schemas/DoesNotContain"}, {"$ref": "#/components/schemas/DoesNotEndWith"}, {"$ref": "#/components/schemas/EndsWith"}, {"$ref": "#/components/schemas/Is"}, {"$ref": "#/components/schemas/IsNot"}]}}}], "required": ["titleSort"]}, "TransientBookDto": {"type": "object", "properties": {"comment": {"type": "string"}, "fileLastModified": {"type": "string", "format": "date-time"}, "files": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "mediaType": {"type": "string"}, "name": {"type": "string"}, "number": {"type": "number", "format": "float"}, "pages": {"type": "array", "items": {"$ref": "#/components/schemas/PageDto"}}, "seriesId": {"type": "string"}, "size": {"type": "string"}, "sizeBytes": {"type": "integer", "format": "int64"}, "status": {"type": "string"}, "url": {"type": "string"}}, "required": ["comment", "fileLastModified", "files", "id", "mediaType", "name", "pages", "size", "sizeBytes", "status", "url"]}, "UserCreationDto": {"type": "object", "properties": {"ageRestriction": {"$ref": "#/components/schemas/AgeRestrictionUpdateDto"}, "email": {"type": "string"}, "labelsAllow": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "labelsExclude": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "password": {"type": "string", "minLength": 1}, "roles": {"type": "array", "items": {"type": "string"}}, "sharedLibraries": {"$ref": "#/components/schemas/SharedLibrariesUpdateDto"}}, "required": ["email", "password", "roles"]}, "UserDto": {"type": "object", "properties": {"ageRestriction": {"$ref": "#/components/schemas/AgeRestrictionDto"}, "email": {"type": "string"}, "id": {"type": "string"}, "labelsAllow": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "labelsExclude": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "sharedAllLibraries": {"type": "boolean"}, "sharedLibrariesIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, "required": ["email", "id", "labelsAllow", "labelsExclude", "roles", "sharedAllLibraries", "sharedLibrariesIds"]}, "UserUpdateDto": {"type": "object", "properties": {"ageRestriction": {"$ref": "#/components/schemas/AgeRestrictionUpdateDto"}, "labelsAllow": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "labelsExclude": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "sharedLibraries": {"$ref": "#/components/schemas/SharedLibrariesUpdateDto"}}}, "ValidationErrorResponse": {"type": "object", "properties": {"violations": {"type": "array", "items": {"$ref": "#/components/schemas/Violation"}}}, "required": ["violations"]}, "Violation": {"type": "object", "properties": {"fieldName": {"type": "string"}, "message": {"type": "string"}}}, "WPBelongsToDto": {"type": "object", "properties": {"collection": {"type": "array", "items": {"$ref": "#/components/schemas/WPContributorDto"}}, "series": {"type": "array", "items": {"$ref": "#/components/schemas/WPContributorDto"}}}, "required": ["collection", "series"]}, "WPContributorDto": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "name": {"type": "string"}, "position": {"type": "number", "format": "float"}}, "required": ["links", "name"]}, "WPLinkDto": {"type": "object", "properties": {"height": {"type": "integer", "format": "int32"}, "href": {"type": "string"}, "properties": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {}}}, "rel": {"type": "string"}, "templated": {"type": "boolean"}, "title": {"type": "string"}, "type": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}, "required": ["properties"]}, "WPMetadataDto": {"type": "object", "properties": {"artist": {"type": "array", "items": {"type": "string"}}, "author": {"type": "array", "items": {"type": "string"}}, "belongsTo": {"$ref": "#/components/schemas/WPBelongsToDto"}, "colorist": {"type": "array", "items": {"type": "string"}}, "conformsTo": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}, "editor": {"type": "array", "items": {"type": "string"}}, "identifier": {"type": "string"}, "illustrator": {"type": "array", "items": {"type": "string"}}, "inker": {"type": "array", "items": {"type": "string"}}, "language": {"type": "string"}, "letterer": {"type": "array", "items": {"type": "string"}}, "modified": {"type": "string", "format": "date-time"}, "numberOfPages": {"type": "integer", "format": "int32"}, "penciler": {"type": "array", "items": {"type": "string"}}, "published": {"type": "string", "format": "date"}, "publisher": {"type": "array", "items": {"type": "string"}}, "readingProgression": {"type": "string", "enum": ["rtl", "ltr", "ttb", "btt", "auto"]}, "rendition": {"type": "object", "additionalProperties": {}}, "sortAs": {"type": "string"}, "subject": {"type": "array", "items": {"type": "string"}}, "subtitle": {"type": "string"}, "title": {"type": "string"}, "translator": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}}, "required": ["artist", "author", "colorist", "contributor", "editor", "illustrator", "inker", "letterer", "penciler", "publisher", "rendition", "subject", "title", "translator"]}, "WPPublicationDto": {"type": "object", "properties": {"context": {"type": "string"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "landmarks": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "metadata": {"$ref": "#/components/schemas/WPMetadataDto"}, "pageList": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "readingOrder": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}, "toc": {"type": "array", "items": {"$ref": "#/components/schemas/WPLinkDto"}}}, "required": ["images", "landmarks", "links", "metadata", "pageList", "readingOrder", "resources", "toc"]}, "WebLinkDto": {"type": "object", "properties": {"label": {"type": "string"}, "url": {"type": "string"}}, "required": ["label", "url"]}, "WebLinkUpdateDto": {"type": "object", "properties": {"label": {"type": "string", "minLength": 1}, "url": {"type": "string"}}, "required": ["label"]}}, "securitySchemes": {"apiKey": {"in": "header", "name": "X-API-Key", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "basicAuth": {"scheme": "basic", "type": "http"}}}, "x-tagGroups": [{"name": "Deprecation", "tags": ["Deprecated"]}, {"name": "Libraries", "tags": ["Libraries"]}, {"name": "Series", "tags": ["Series", "Series Poster"]}, {"name": "Books", "tags": ["Books", "Book Pages", "Book Poster", "Import", "Duplicate Pages", "WebPub Manifest", "Fonts"]}, {"name": "Collections", "tags": ["Collections", "Collection Series", "Collection Poster"]}, {"name": "Readlists", "tags": ["Readlists", "Readlist Books", "Readlist Poster"]}, {"name": "Referential", "tags": ["Referential metadata"]}, {"name": "Users", "tags": ["Current user", "Users", "API Keys", "User session", "OAuth2", "Sync points"]}, {"name": "Server", "tags": ["<PERSON><PERSON><PERSON>", "Server settings", "Tasks", "History", "File system", "Releases", "Announcements", "Management"]}, {"name": "Integrations", "tags": ["Client settings", "<PERSON><PERSON>", "ComicRack"]}]}