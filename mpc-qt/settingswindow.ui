<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SettingsWindow</class>
 <widget class="QWidget" name="SettingsWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>896</width>
    <height>638</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Options</string>
  </property>
  <layout class="QVBoxLayout" name="settingsWindowLayout">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <widget class="QTreeWidget" name="pageTree">
      <property name="verticalScrollBarPolicy">
       <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
      </property>
      <property name="horizontalScrollBarPolicy">
       <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
      </property>
      <property name="headerHidden">
       <bool>true</bool>
      </property>
      <column>
       <property name="text">
        <string>1</string>
       </property>
      </column>
      <item>
       <property name="text">
        <string>Player</string>
       </property>
       <item>
        <property name="text">
         <string>Keys</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Logo</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Interface</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Stylesheet</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Web Interface</string>
        </property>
       </item>
      </item>
      <item>
       <property name="text">
        <string>Playback</string>
       </property>
       <item>
        <property name="text">
         <string>Output</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Shaders</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Fullscreen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Sync</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Hw. Decoding</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Playlist</string>
        </property>
       </item>
      </item>
      <item>
       <property name="text">
        <string>Audio</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Subtitles</string>
       </property>
       <item>
        <property name="text">
         <string>Default Style</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Misc</string>
        </property>
       </item>
      </item>
      <item>
       <property name="text">
        <string>Export</string>
       </property>
       <item>
        <property name="text">
         <string>Encoding</string>
        </property>
       </item>
      </item>
      <item>
       <property name="text">
        <string>Tweaks</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Logging</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Miscellaneous</string>
       </property>
      </item>
     </widget>
     <widget class="QWidget" name="stackHost" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <layout class="QVBoxLayout" name="stackHostLayout" stretch="0,0">
       <item>
        <widget class="QLabel" name="pageLabel">
         <property name="text">
          <string>&lt;big&gt;&lt;b&gt;Player</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QStackedWidget" name="pageStack">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="currentIndex">
          <number>7</number>
         </property>
         <widget class="QWidget" name="playerPage">
          <layout class="QGridLayout" name="playerPageLayout">
           <item row="2" column="1">
            <widget class="QGroupBox" name="playerHistoryBox">
             <property name="title">
              <string>History</string>
             </property>
             <layout class="QVBoxLayout" name="playerHistoryBoxLayout">
              <item>
               <widget class="QCheckBox" name="playerKeepHistory">
                <property name="text">
                 <string>Keep history of recently opened files</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerKeepHistoryOnlyForVideos">
                <property name="text">
                 <string>Only keep history for videos</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerRememberFilePosition">
                <property name="text">
                 <string>Remember file position</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerRememberLastPlaylist">
                <property name="text">
                 <string>Remember last selected playlist</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerRememberWindowGeometry">
                <property name="text">
                 <string>Remember last window geometry</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerRememberPanels">
                <property name="text">
                 <string>Remember panels state</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerRememberPanScanZoom">
                <property name="text">
                 <string>Remember last Pan-n-Scan Zoom</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="playerHistorySpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QGroupBox" name="playerLanguageBox">
             <property name="toolTip">
              <string>Requires restarting the application to apply changes</string>
             </property>
             <property name="title">
              <string>Language Override</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_17">
              <item>
               <widget class="QComboBox" name="playerLanguageComboBox_v2">
                <property name="currentIndex">
                 <number>0</number>
                </property>
                <item>
                 <property name="text">
                  <string>Locale (Autodetect)</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true" extracomment="en - English">English</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QGroupBox" name="playerTitleBox">
             <property name="title">
              <string>Title bar</string>
             </property>
             <layout class="QVBoxLayout" name="playerTitleBoxLayout">
              <item>
               <widget class="QRadioButton" name="playerTitleDisplayFullPath">
                <property name="text">
                 <string>Disp&amp;lay full path</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="playerTitleFileNameOnly">
                <property name="text">
                 <string>File &amp;name only</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="playerTitleDontPrefix">
                <property name="text">
                 <string>Don't prefi&amp;x anything</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerTitleReplaceName">
                <property name="text">
                 <string>Replace file name with title</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="playerTitleSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QGroupBox" name="playerOpenBox">
             <property name="title">
              <string>Open options</string>
             </property>
             <layout class="QVBoxLayout" name="playerOpenBoxLayout">
              <item>
               <widget class="QRadioButton" name="playerOpenSame">
                <property name="text">
                 <string>Use the same player for
each &amp;media file</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="playerOpenNew">
                <property name="text">
                 <string>Open a new &amp;player for each
media file played</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="playerOpenSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item row="0" column="1" rowspan="2">
            <widget class="QGroupBox" name="playerOtherBox">
             <property name="title">
              <string>Other</string>
             </property>
             <layout class="QVBoxLayout" name="playerOtherBoxLayout">
              <item>
               <widget class="QCheckBox" name="playerTrayIcon">
                <property name="text">
                 <string>Tray icon</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerOSD">
                <property name="text">
                 <string>Show OSD</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerLimitProportions">
                <property name="text">
                 <string>Limit window proportions on resize</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerDisableOpenDisc">
                <property name="text">
                 <string>Disable &quot;Open Disc&quot; menu</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playerDisableScreensaver">
                <property name="text">
                 <string>Disable screensaver while playing</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="playerOtherSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="keysPage">
          <layout class="QVBoxLayout" name="keysPageLayout" stretch="1,0,0">
           <item>
            <widget class="QLineEdit" name="keysSearchField">
             <property name="placeholderText">
              <string>Search...</string>
             </property>
             <property name="clearButtonEnabled">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QVBoxLayout" name="keysHost"/>
           </item>
           <item>
            <layout class="QHBoxLayout" name="keysBottomLayout" stretch="1,0">
             <item>
              <layout class="QVBoxLayout" name="ipcLayout" stretch="0,0">
               <item>
                <widget class="QLabel" name="ipcNotice">
                 <property name="text">
                  <string>&lt;a href=&quot;https://github.com/mpc-qt/mpc-qt/blob/master/DOCS/ipc.md&quot;&gt;JSON IPC&lt;/a&gt; available at %1</string>
                 </property>
                 <property name="openExternalLinks">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QCheckBox" name="ipcMpris">
                 <property name="text">
                  <string>MPRIS</string>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QVBoxLayout" name="keysLayout" stretch="0">
               <item>
                <widget class="QPushButton" name="keysReset">
                 <property name="text">
                  <string>Reset</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="logoPage">
          <layout class="QVBoxLayout" name="logoPageLayout" stretch="1,0">
           <item>
            <layout class="QVBoxLayout" name="logoImageHost"/>
           </item>
           <item>
            <layout class="QGridLayout" name="logoLayout">
             <item row="1" column="0">
              <widget class="QRadioButton" name="logoExternal">
               <property name="text">
                <string>E&amp;xternal</string>
               </property>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QRadioButton" name="logoUseInternal">
               <property name="text">
                <string>Interna&amp;l</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLineEdit" name="logoExternalLocation"/>
             </item>
             <item row="1" column="2">
              <widget class="QPushButton" name="logoExternalBrowse">
               <property name="text">
                <string>Browse...</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1" colspan="2">
              <widget class="QComboBox" name="logoInternal">
               <property name="currentIndex">
                <number>1</number>
               </property>
               <item>
                <property name="text">
                 <string>Blank</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>Cinema screen</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>Triangle in circle</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>Multi purpose vehicle</string>
                </property>
               </item>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="interfacePage">
          <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,1,0">
           <item>
            <widget class="QGroupBox" name="interfaceIconsBox">
             <property name="title">
              <string>Icons</string>
             </property>
             <layout class="QFormLayout" name="formLayout_5">
              <item row="0" column="0">
               <widget class="QLabel" name="interfaceIconsThemeLabel">
                <property name="text">
                 <string>Theme</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QComboBox" name="interfaceIconsTheme">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <item>
                 <property name="text">
                  <string>Built-in fallback</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Custom</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>System (Linux only)</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="interfaceIconsCustomLabel">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="text">
                 <string>Custom</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <layout class="QHBoxLayout" name="interfaceIconsCustomLayout">
                <item>
                 <widget class="QLineEdit" name="interfaceIconsCustomFolder">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="placeholderText">
                   <string>Folder (e.g. ~/Pictures/MyIcons/mpc-qt/leet)</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="interfaceIconsCustomBrowse">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="text">
                   <string>Browse...</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="3" column="1">
               <widget class="QLabel" name="interfaceIconsNotice">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="text">
                 <string>See &lt;a href=&quot;https://github.com/mpc-qt/mpc-qt/tree/master/images/theme/black&quot;&gt;source repo&lt;/a&gt; for icon names.</string>
                </property>
                <property name="openExternalLinks">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="interfaceIconsInbuiltLabel">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="text">
                 <string>Fallback</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="interfaceIconsInbuilt">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <item>
                 <property name="text">
                  <string>Auto</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Black (for white palette)</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>White (for black palette)</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="interfaceWidgetBox">
             <property name="title">
              <string>Widget Color</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <widget class="QCheckBox" name="interfaceWidgetHighContast">
                <property name="text">
                 <string>High-contrast timeline and volume sliders</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="interfaceWidgetCustom">
                <property name="text">
                 <string>Use custom theme</string>
                </property>
                <property name="checked">
                 <bool>false</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QScrollArea" name="interfaceWidgetCustomScrollArea">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="horizontalScrollBarPolicy">
                 <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
                </property>
                <property name="widgetResizable">
                 <bool>true</bool>
                </property>
                <widget class="QWidget" name="interfaceWidgetCustomHost">
                 <property name="geometry">
                  <rect>
                   <x>0</x>
                   <y>0</y>
                   <width>96</width>
                   <height>26</height>
                  </rect>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_6"/>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="interfaceWindowBox">
             <property name="title">
              <string>Window Color</string>
             </property>
             <layout class="QFormLayout" name="formLayout_6">
              <item row="0" column="0">
               <widget class="QLabel" name="windowVideoLabel">
                <property name="text">
                 <string>Video</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <layout class="QHBoxLayout" name="windowVideoLayout">
                <item>
                 <widget class="QLineEdit" name="windowVideoValue">
                  <property name="inputMask">
                   <string>HHHHHH</string>
                  </property>
                  <property name="text">
                   <string notr="true">000000</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="windowVideoPick">
                  <property name="styleSheet">
                   <string notr="true">background: #000000;</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="windowInfoBackgroundLabel">
                <property name="text">
                 <string>Info Background</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <layout class="QHBoxLayout" name="windowInfoBackgroundLayout">
                <item>
                 <widget class="QLineEdit" name="windowInfoBackgroundValue">
                  <property name="inputMask">
                   <string>HHHHHH</string>
                  </property>
                  <property name="text">
                   <string notr="true">000000</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="windowInfoBackgroundPick">
                  <property name="styleSheet">
                   <string notr="true">background: #000000;</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="windowInfoForegroundLabel">
                <property name="text">
                 <string>Info Foreground</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <layout class="QHBoxLayout" name="windowInfoForegroundLayout">
                <item>
                 <widget class="QLineEdit" name="windowInfoForegroundValue">
                  <property name="inputMask">
                   <string>HHHHHH</string>
                  </property>
                  <property name="text">
                   <string notr="true">FFFFFF</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="windowInfoForegroundPick">
                  <property name="styleSheet">
                   <string notr="true">background: #FFFFFF;</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="stylesheetPage">
          <layout class="QVBoxLayout" name="verticalLayout_15">
           <item>
            <widget class="QCheckBox" name="stylesheetFusion">
             <property name="toolTip">
              <string>Allows dark theme support on Windows</string>
             </property>
             <property name="text">
              <string>Use Qt's inbuilt fusion style</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPlainTextEdit" name="stylesheetText">
             <property name="placeholderText">
              <string>Application Stylesheet</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="webPage">
          <layout class="QVBoxLayout" name="verticalLayout_10">
           <item>
            <widget class="QGroupBox" name="webTcpIpBox">
             <property name="title">
              <string>TCP/IP</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_12">
              <item>
               <widget class="QCheckBox" name="webEnableServer">
                <property name="text">
                 <string>Enable web server</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="webTcpIpOptions" native="true">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <layout class="QFormLayout" name="webTcpIpOptionsLayout">
                 <item row="0" column="0">
                  <widget class="QLabel" name="webPortLabel">
                   <property name="text">
                    <string>Port</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <layout class="QHBoxLayout" name="webListenLayout" stretch="1,1">
                   <item>
                    <widget class="QSpinBox" name="webPort">
                     <property name="minimum">
                      <number>1</number>
                     </property>
                     <property name="maximum">
                      <number>65535</number>
                     </property>
                     <property name="value">
                      <number>13579</number>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="webPortLink">
                     <property name="text">
                      <string>&lt;a href=&quot;#&quot;&gt;Launch in web browser...&lt;/a&gt;</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="1" column="0">
                  <widget class="QLabel" name="webLocalhostLabel">
                   <property name="text">
                    <string>Security</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="1">
                  <widget class="QCheckBox" name="webLocalhost_v2">
                   <property name="text">
                    <string>Allow access from localhost only</string>
                   </property>
                   <property name="checked">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="webLocalFilesBox">
             <property name="title">
              <string>Local files</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_11">
              <item>
               <widget class="QCheckBox" name="webServePages">
                <property name="text">
                 <string>Serve pages from disk</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="webLocalFilesOptions" native="true">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <layout class="QFormLayout" name="webLocalFilesOptionsLayout">
                 <item row="0" column="0">
                  <widget class="QLabel" name="webRootLabel">
                   <property name="text">
                    <string>Web root</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <layout class="QHBoxLayout" name="servePagesLayout">
                   <item>
                    <widget class="QLineEdit" name="webRoot">
                     <property name="placeholderText">
                      <string>webroot</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="webRootBrowse">
                     <property name="text">
                      <string>Browse</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item row="1" column="0">
                  <widget class="QLabel" name="webDefaultPageLabel">
                   <property name="text">
                    <string>Default page</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="1">
                  <widget class="QLineEdit" name="webDefaultPage">
                   <property name="placeholderText">
                    <string>index.html</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_5">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="playbackPage">
          <layout class="QGridLayout" name="playbackPageLayout" columnstretch="1,1">
           <item row="1" column="0">
            <widget class="QGroupBox" name="playbackControlBox">
             <property name="title">
              <string>Control</string>
             </property>
             <layout class="QFormLayout" name="playbackControlBoxLayout">
              <item row="0" column="0">
               <widget class="QLabel" name="playbackVolumeStepLabel">
                <property name="text">
                 <string>Volume step</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QSpinBox" name="playbackVolumeStep">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true">%</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="value">
                 <number>10</number>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="playbackSpeedStepLabel">
                <property name="text">
                 <string>Speed step</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <layout class="QHBoxLayout" name="playbackSpeedStepLayout" stretch="1,0">
                <item>
                 <widget class="QSpinBox" name="playbackSpeedStep">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="specialValueText">
                   <string>Auto</string>
                  </property>
                  <property name="suffix">
                   <string notr="true">%</string>
                  </property>
                  <property name="maximum">
                   <number>100</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QCheckBox" name="playbackSpeedStepAdditive">
                  <property name="text">
                   <string>Use additive speed step</string>
                  </property>
                  <property name="checked">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="playbackNormalStepLabel">
                <property name="text">
                 <string>Normal step</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QSpinBox" name="playbackNormalStep">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true"> ms</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>60000</number>
                </property>
                <property name="value">
                 <number>5000</number>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="playbackLargeStepLabel">
                <property name="text">
                 <string>Large step</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QSpinBox" name="playbackLargeStep">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true"> ms</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>60000</number>
                </property>
                <property name="value">
                 <number>20000</number>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QGroupBox" name="playbackAutoBox">
             <property name="title">
              <string>Output</string>
             </property>
             <layout class="QFormLayout" name="playbackAutoBoxLayout">
              <item row="0" column="0" colspan="2">
               <widget class="QCheckBox" name="playbackAutoCenterWindow">
                <property name="text">
                 <string>Center window when zooming</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QCheckBox" name="playbackAutoZoom">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>Auto zoom</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="playbackAutoZoomMethod">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="currentText">
                 <string>100%</string>
                </property>
                <property name="currentIndex">
                 <number>3</number>
                </property>
                <item>
                 <property name="text">
                  <string>25%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>50%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>75%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>100%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>150%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>200%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>300%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>400%</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Autofit</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Autofit (Larger Only)</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Autofit (Smaller Only)</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="playbackAutoFitFactorLabel">
                <property name="text">
                 <string>Auto fit factor</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QSpinBox" name="playbackAutoFitFactor">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true">%</string>
                </property>
                <property name="minimum">
                 <number>25</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>75</number>
                </property>
               </widget>
              </item>
              <item row="4" column="0" colspan="2">
               <widget class="QLabel" name="playbackAutozoomWarn">
                <property name="text">
                 <string>Autofitting in tiling window managers requires that the window be in floating mode.</string>
                </property>
                <property name="wordWrap">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="4" column="0">
            <spacer name="playbackSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="2" column="1">
            <widget class="QGroupBox" name="playbackTracksBox">
             <property name="title">
              <string>Default track preference</string>
             </property>
             <layout class="QFormLayout" name="playbackTracksBoxLayout">
              <item row="0" column="0">
               <widget class="QLabel" name="playbackSubtitleTracksLabel">
                <property name="text">
                 <string>Subtitles</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLineEdit" name="playbackSubtitleTracks">
                <property name="placeholderText">
                 <string notr="true">en,eng</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="playbackAudioTracksLabel">
                <property name="text">
                 <string>Audio</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLineEdit" name="playbackAudioTracks">
                <property name="placeholderText">
                 <string notr="true">jpn,eng</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QGroupBox" name="playbackMouseHideBox">
             <property name="title">
              <string>Mouse hiding</string>
             </property>
             <layout class="QGridLayout" name="playbackMouseHideBoxLayout">
              <item row="0" column="0">
               <widget class="QCheckBox" name="playbackMouseHideFullscreen">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>Fullscreen</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QSpinBox" name="playbackMouseHideFullscreenDuration">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true"> ms</string>
                </property>
                <property name="minimum">
                 <number>10</number>
                </property>
                <property name="maximum">
                 <number>30000</number>
                </property>
                <property name="value">
                 <number>1000</number>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QCheckBox" name="playbackMouseHideWindowed">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>Windowed</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QSpinBox" name="playbackMouseHideWindowedDuration">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true"> ms</string>
                </property>
                <property name="minimum">
                 <number>10</number>
                </property>
                <property name="maximum">
                 <number>30000</number>
                </property>
                <property name="value">
                 <number>1000</number>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QGroupBox" name="afterPlaybackBox">
             <property name="title">
              <string>After Playback</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_16">
              <item>
               <widget class="QLabel" name="afterPlaybackDefaultLabel">
                <property name="text">
                 <string>Default After Playback action:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="afterPlaybackDefault">
                <property name="currentIndex">
                 <number>0</number>
                </property>
                <item>
                 <property name="text">
                  <string>Do nothing</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Repeat</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Play next file</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Exit</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QGroupBox" name="ytdlpBox">
             <property name="title">
              <string>yt-dlp (web videos)</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_18">
              <item>
               <widget class="QLabel" name="ytdlpLabel">
                <property name="text">
                 <string>Max video height:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="ytdlpMaxHeight">
                <property name="currentIndex">
                 <number>4</number>
                </property>
                <item>
                 <property name="text">
                  <string notr="true">240</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">360</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">480</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">720</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">1080</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">1440</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">2160</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">2880</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string notr="true">4320</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="outputPage">
          <layout class="QVBoxLayout" name="outputPageLayout">
           <item>
            <widget class="QGroupBox" name="videoBox">
             <property name="title">
              <string>Video Renderer</string>
             </property>
             <layout class="QVBoxLayout" name="videoBoxLayout">
              <item>
               <widget class="QCheckBox" name="videoDumbMode">
                <property name="text">
                 <string>Dumb mode</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QTabWidget" name="videoTabs">
                <property name="currentIndex">
                 <number>0</number>
                </property>
                <widget class="QWidget" name="generalTab">
                 <attribute name="title">
                  <string>General</string>
                 </attribute>
                 <layout class="QFormLayout" name="generalTabLayout">
                  <property name="fieldGrowthPolicy">
                   <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                  </property>
                  <item row="0" column="0">
                   <widget class="QLabel" name="videoFramebufferLabel">
                    <property name="text">
                     <string>Framebuffer</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <layout class="QHBoxLayout" name="videoFramebufferLayout" stretch="1,1,2">
                    <item>
                     <widget class="QComboBox" name="videoFramebuffer">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="currentIndex">
                       <number>0</number>
                      </property>
                      <item>
                       <property name="text">
                        <string>8 bits</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>10 bits</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>12 bits</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>16 bits</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>16 bits float</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>32 bits float</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                    <item>
                     <widget class="QCheckBox" name="videoUseAlpha">
                      <property name="text">
                       <string>Alpha channel</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <spacer name="videoFramebufferSpacer">
                      <property name="orientation">
                       <enum>Qt::Orientation::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="videoAlphaModeLabel">
                    <property name="text">
                     <string>Alpha</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QComboBox" name="videoAlphaMode">
                    <item>
                     <property name="text">
                      <string>Blend on black</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Use</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Ignore</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QLabel" name="videoSharpenLabel">
                    <property name="text">
                     <string>Sharpen</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QDoubleSpinBox" name="videoSharpen">
                    <property name="specialValueText">
                     <string>Disabled</string>
                    </property>
                    <property name="decimals">
                     <number>6</number>
                    </property>
                    <property name="maximum">
                     <double>20.000000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QLabel" name="videoPresetLabel">
                    <property name="text">
                     <string>Presets</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <layout class="QHBoxLayout" name="videoPresetLayout" stretch="1,0,2">
                    <item>
                     <widget class="QComboBox" name="videoPreset">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <item>
                       <property name="text">
                        <string>Select</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Plain</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Low</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Medium</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>High</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Highest</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Placebo</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                    <item>
                     <widget class="QLabel" name="videoPresetApplied">
                      <property name="text">
                       <string>Preset applied</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <spacer name="videoPresetSpacer">
                      <property name="orientation">
                       <enum>Qt::Orientation::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="ditherTab">
                 <attribute name="title">
                  <string>Dither</string>
                 </attribute>
                 <layout class="QHBoxLayout" name="ditherTabLayout">
                  <property name="spacing">
                   <number>15</number>
                  </property>
                  <item>
                   <widget class="QFrame" name="ditherFrameLeft">
                    <layout class="QFormLayout" name="ditherFrameLeftLayout">
                     <property name="leftMargin">
                      <number>0</number>
                     </property>
                     <property name="topMargin">
                      <number>0</number>
                     </property>
                     <property name="rightMargin">
                      <number>0</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item row="0" column="0" colspan="2">
                      <widget class="QCheckBox" name="ditherDithering">
                       <property name="text">
                        <string>Dithering</string>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="0">
                      <widget class="QLabel" name="ditherDepthLabel">
                       <property name="text">
                        <string>Depth</string>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="1">
                      <widget class="QSpinBox" name="ditherDepth">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="specialValueText">
                        <string>Auto</string>
                       </property>
                       <property name="maximum">
                        <number>8</number>
                       </property>
                      </widget>
                     </item>
                     <item row="2" column="0">
                      <widget class="QLabel" name="ditherTypeLabel">
                       <property name="text">
                        <string>Type</string>
                       </property>
                      </widget>
                     </item>
                     <item row="2" column="1">
                      <widget class="QComboBox" name="ditherType">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <item>
                        <property name="text">
                         <string>Fruit</string>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>Ordered</string>
                        </property>
                       </item>
                       <item>
                        <property name="text">
                         <string>No</string>
                        </property>
                       </item>
                      </widget>
                     </item>
                     <item row="3" column="0">
                      <widget class="QLabel" name="ditherFruitSizeLabel">
                       <property name="text">
                        <string>Size</string>
                       </property>
                      </widget>
                     </item>
                     <item row="3" column="1">
                      <widget class="QSpinBox" name="ditherFruitSize">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="minimum">
                        <number>2</number>
                       </property>
                       <property name="maximum">
                        <number>8</number>
                       </property>
                       <property name="value">
                        <number>4</number>
                       </property>
                      </widget>
                     </item>
                     <item row="4" column="0" colspan="2">
                      <widget class="QLabel" name="ditherLCDUgly">
                       <property name="text">
                        <string>Often, LCDs perform dithering on their own, which conflicts with OpenGL's output and can lead to ugly output.  In which case you should lower the dither depth.</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignmentFlag::AlignJustify|Qt::AlignmentFlag::AlignTop</set>
                       </property>
                       <property name="wordWrap">
                        <bool>true</bool>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                  <item>
                   <widget class="QFrame" name="ditherFrameRight">
                    <layout class="QFormLayout" name="ditherFrameRightLayout">
                     <property name="leftMargin">
                      <number>0</number>
                     </property>
                     <property name="topMargin">
                      <number>0</number>
                     </property>
                     <property name="rightMargin">
                      <number>0</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item row="0" column="0" colspan="2">
                      <widget class="QCheckBox" name="ditherTemporal">
                       <property name="text">
                        <string>Temporal dithering</string>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="0">
                      <widget class="QLabel" name="ditherTemporalPeriodLabel">
                       <property name="text">
                        <string>Period</string>
                       </property>
                      </widget>
                     </item>
                     <item row="1" column="1">
                      <widget class="QSpinBox" name="ditherTemporalPeriod">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="minimum">
                        <number>1</number>
                       </property>
                       <property name="maximum">
                        <number>128</number>
                       </property>
                      </widget>
                     </item>
                     <item row="2" column="0" colspan="2">
                      <widget class="QLabel" name="ditherLCDFlicker">
                       <property name="text">
                        <string>This can lead to flicker on LCD displays, since these  have a high reaction time.</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignmentFlag::AlignJustify|Qt::AlignmentFlag::AlignTop</set>
                       </property>
                       <property name="wordWrap">
                        <bool>true</bool>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="scalingTab">
                 <attribute name="title">
                  <string>Scaling</string>
                 </attribute>
                 <layout class="QVBoxLayout" name="scalingTabLayout">
                  <item>
                   <widget class="QTabWidget" name="scalingTabs">
                    <property name="currentIndex">
                     <number>0</number>
                    </property>
                    <widget class="QWidget" name="scaleOptionsTab">
                     <attribute name="title">
                      <string>Options</string>
                     </attribute>
                     <layout class="QHBoxLayout" name="scaleOptionsTabLayout" stretch="1,1">
                      <item>
                       <layout class="QVBoxLayout" name="scalingLayout">
                        <item>
                         <widget class="QCheckBox" name="scalingCorrectDownscaling">
                          <property name="text">
                           <string>Correct downscaling</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="scalingInLinearLight">
                          <property name="text">
                           <string>Downscale in linear light</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="scalingUpInLinearLight">
                          <property name="text">
                           <string>Upscale in linear light</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="scalingTemporalInterpolation">
                          <property name="text">
                           <string>Temporal interpolation</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="scalingBlendSubtitles">
                          <property name="text">
                           <string>Blend subtitles</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="scalingSpacer">
                          <property name="orientation">
                           <enum>Qt::Orientation::Vertical</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>20</width>
                            <height>40</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <layout class="QVBoxLayout" name="sigmoidLayout">
                        <property name="bottomMargin">
                         <number>5</number>
                        </property>
                        <item>
                         <widget class="QGroupBox" name="sigmoidBox">
                          <property name="title">
                           <string/>
                          </property>
                          <layout class="QFormLayout" name="sigmoidBoxLayout">
                           <item row="0" column="0" colspan="2">
                            <widget class="QCheckBox" name="scalingSigmoidizedUpscaling">
                             <property name="text">
                              <string>Sigmoidized upscaling</string>
                             </property>
                            </widget>
                           </item>
                           <item row="1" column="0">
                            <widget class="QLabel" name="sigmoidizedCenterLabel">
                             <property name="text">
                              <string>Center</string>
                             </property>
                            </widget>
                           </item>
                           <item row="1" column="1">
                            <widget class="QDoubleSpinBox" name="sigmoidizedCenter">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="decimals">
                              <number>6</number>
                             </property>
                             <property name="maximum">
                              <double>1.000000000000000</double>
                             </property>
                             <property name="value">
                              <double>0.750000000000000</double>
                             </property>
                            </widget>
                           </item>
                           <item row="2" column="0">
                            <widget class="QLabel" name="sigmoidizedSlopeLabel">
                             <property name="text">
                              <string>Slope</string>
                             </property>
                            </widget>
                           </item>
                           <item row="2" column="1">
                            <widget class="QDoubleSpinBox" name="sigmoidizedSlope">
                             <property name="sizePolicy">
                              <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                               <horstretch>0</horstretch>
                               <verstretch>0</verstretch>
                              </sizepolicy>
                             </property>
                             <property name="decimals">
                              <number>6</number>
                             </property>
                             <property name="minimum">
                              <double>1.000000000000000</double>
                             </property>
                             <property name="maximum">
                              <double>20.000000000000000</double>
                             </property>
                             <property name="value">
                              <double>6.500000000000000</double>
                             </property>
                            </widget>
                           </item>
                          </layout>
                         </widget>
                        </item>
                       </layout>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="scaleTab">
                     <attribute name="title">
                      <string>Scale</string>
                     </attribute>
                     <layout class="QGridLayout" name="scaleTabLayout">
                      <item row="0" column="0">
                       <widget class="QLabel" name="scaleScalerLabel">
                        <property name="text">
                         <string>Scaler</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="0">
                       <widget class="QLabel" name="scaleParam1Label">
                        <property name="text">
                         <string>1</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="1">
                       <layout class="QHBoxLayout" name="scaleParam1Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleParam1Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleParam1Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="1" column="3">
                       <layout class="QHBoxLayout" name="scaleRadiusLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleRadiusSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleRadiusValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>16.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="value">
                           <double>2.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="0">
                       <widget class="QLabel" name="scaleParam2Label">
                        <property name="text">
                         <string>2</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="1">
                       <layout class="QHBoxLayout" name="scaleParam2Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleParam2Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleParam2Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="2">
                       <widget class="QLabel" name="scaleAntiRingLabel">
                        <property name="text">
                         <string>Anti-ring</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="3">
                       <layout class="QHBoxLayout" name="scaleAntiRingLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleAntiRingSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleAntiRingValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>0.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="0">
                       <widget class="QLabel" name="scaleBlurLabel">
                        <property name="text">
                         <string>Blur</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="1">
                       <layout class="QHBoxLayout" name="scaleBlurLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleBlurSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleBlurValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="0">
                       <widget class="QLabel" name="scaleWindowLabel">
                        <property name="text">
                         <string>Window</string>
                        </property>
                       </widget>
                      </item>
                      <item row="4" column="1">
                       <layout class="QHBoxLayout" name="scaleWindowLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleWindowSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QComboBox" name="scaleWindowValue">
                          <item>
                           <property name="text">
                            <string>Box</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Triangle</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Bartlett</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hanning</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hamming</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Quadric</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Welch</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Kaiser</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Blackman</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Gaussian</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Jinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sphinx</string>
                           </property>
                          </item>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="2" colspan="2">
                       <layout class="QHBoxLayout" name="scaleWindowParamLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleWindowParamSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleWindowParamValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="3">
                       <layout class="QHBoxLayout" name="scaleClampLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="scaleClampSet"/>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="scaleClampValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="2">
                       <widget class="QLabel" name="scaleClampLabel">
                        <property name="text">
                         <string>Clamp</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="2">
                       <widget class="QLabel" name="scaleRadiusLabel">
                        <property name="text">
                         <string>Radius</string>
                        </property>
                       </widget>
                      </item>
                      <item row="0" column="1" colspan="3">
                       <widget class="QComboBox" name="scaleScaler">
                        <item>
                         <property name="text">
                          <string>Bilinear</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic_fast</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Oversample</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline16</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline36</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline64</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Sinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Jinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa hanning</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos soft</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Haasnsoft</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bc spline</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Catmull rom</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Mitchell</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Box</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Nearest</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Triangle</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Gaussian</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="dscaleTab">
                     <attribute name="title">
                      <string>Downscale</string>
                     </attribute>
                     <layout class="QGridLayout" name="dscaleTabLayout">
                      <item row="0" column="0">
                       <widget class="QLabel" name="dscaleScalerLabel">
                        <property name="text">
                         <string>Scaler</string>
                        </property>
                       </widget>
                      </item>
                      <item row="0" column="1" colspan="3">
                       <widget class="QComboBox" name="dscaleScaler">
                        <item>
                         <property name="text">
                          <string>Unset</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bilinear</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic_fast</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Oversample</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline16</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline36</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline64</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Sinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Jinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa hanning</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos soft</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Haasnsoft</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bc spline</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Catmull rom</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Mitchell</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Box</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Nearest</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Triangle</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Gaussian</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                      <item row="1" column="0">
                       <widget class="QLabel" name="dscaleParam1Label">
                        <property name="text">
                         <string>1</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="1">
                       <layout class="QHBoxLayout" name="dscaleParam1Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleParam1Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleParam1Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="1" column="2">
                       <widget class="QLabel" name="dscaleRadiusLabel">
                        <property name="text">
                         <string>Radius</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="3">
                       <layout class="QHBoxLayout" name="dscaleRadiusLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleRadiusSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleRadiusValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>16.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="value">
                           <double>2.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="0">
                       <widget class="QLabel" name="dscaleParam2Label">
                        <property name="text">
                         <string>2</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="1">
                       <layout class="QHBoxLayout" name="dscaleParam2Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleParam2Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleParam2Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="2">
                       <widget class="QLabel" name="dscaleAntiRingLabel">
                        <property name="text">
                         <string>Anti-ring</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="3">
                       <layout class="QHBoxLayout" name="dscaleAntiRingLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleAntiRingSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleAntiRingValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>0.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="0">
                       <widget class="QLabel" name="dscaleBlurLabel">
                        <property name="text">
                         <string>Blur</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="1">
                       <layout class="QHBoxLayout" name="dscaleBlurLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleBlurSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleBlurValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="2">
                       <widget class="QLabel" name="dscaleClampLabel">
                        <property name="text">
                         <string>Clamp</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="3">
                       <layout class="QHBoxLayout" name="dscaleClampLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleClampSet"/>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleClampValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="0">
                       <widget class="QLabel" name="dscaleWindowLabel">
                        <property name="text">
                         <string>Window</string>
                        </property>
                       </widget>
                      </item>
                      <item row="4" column="1">
                       <layout class="QHBoxLayout" name="dscaleWindowLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleWindowSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QComboBox" name="dscaleWindowValue">
                          <item>
                           <property name="text">
                            <string>Box</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Triangle</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Bartlett</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hanning</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hamming</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Quadric</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Welch</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Kaiser</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Blackman</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Gaussian</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Jinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sphinx</string>
                           </property>
                          </item>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="2" colspan="2">
                       <layout class="QHBoxLayout" name="dscaleWindowParamLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="dscaleWindowParamSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="dscaleWindowParamValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="cscaleTab">
                     <attribute name="title">
                      <string>Color</string>
                     </attribute>
                     <layout class="QGridLayout" name="cscaleTabLayout">
                      <item row="0" column="0">
                       <widget class="QLabel" name="cscaleScalerLabel">
                        <property name="text">
                         <string>Scaler</string>
                        </property>
                       </widget>
                      </item>
                      <item row="0" column="1" colspan="3">
                       <widget class="QComboBox" name="cscaleScaler">
                        <item>
                         <property name="text">
                          <string>Bilinear</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic_fast</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Oversample</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline16</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline36</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline64</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Sinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Jinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa hanning</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa lanczos soft</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Haasnsoft</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bc spline</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Catmull rom</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Mitchell</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ewa robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Box</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Nearest</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Triangle</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Gaussian</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                      <item row="1" column="0">
                       <widget class="QLabel" name="cscaleParam1Label">
                        <property name="text">
                         <string>1</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="1">
                       <layout class="QHBoxLayout" name="cscaleParam1Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleParam1Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleParam1Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="1" column="2">
                       <widget class="QLabel" name="cscaleRadiusLabel">
                        <property name="text">
                         <string>Radius</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="3">
                       <layout class="QHBoxLayout" name="cscaleRadiusLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleRadiusSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleRadiusValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>16.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="value">
                           <double>2.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="0">
                       <widget class="QLabel" name="cscaleParam2Label">
                        <property name="text">
                         <string>2</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="1">
                       <layout class="QHBoxLayout" name="cscaleParam2Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleParam2Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleParam2Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="2">
                       <widget class="QLabel" name="cscaleAntiRingLabel">
                        <property name="text">
                         <string>Anti-ring</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="3">
                       <layout class="QHBoxLayout" name="cscaleAntiRingLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleAntiRingSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleAntiRingValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="0">
                       <widget class="QLabel" name="cscaleBlurLabel">
                        <property name="text">
                         <string>Blur</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="1">
                       <layout class="QHBoxLayout" name="cscaleBlurLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleBlurSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleBlurValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="2">
                       <widget class="QLabel" name="cscaleClampLabel">
                        <property name="text">
                         <string>Clamp</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="3">
                       <layout class="QHBoxLayout" name="cscaleClampLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleClampSet"/>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleClampValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="0">
                       <widget class="QLabel" name="cscaleWindowLabel">
                        <property name="text">
                         <string>Window</string>
                        </property>
                       </widget>
                      </item>
                      <item row="4" column="1">
                       <layout class="QHBoxLayout" name="cscaleWindowLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleWindowSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QComboBox" name="cscaleWindowValue">
                          <item>
                           <property name="text">
                            <string>Box</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Triangle</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Bartlett</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hanning</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hamming</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Quadric</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Welch</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Kaiser</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Blackman</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Gaussian</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Jinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sphinx</string>
                           </property>
                          </item>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="2" colspan="2">
                       <layout class="QHBoxLayout" name="cscaleWindowParamLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="cscaleWindowParamSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="cscaleWindowParamValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="tscaleTab">
                     <attribute name="title">
                      <string>Temporal</string>
                     </attribute>
                     <layout class="QGridLayout" name="tscaleTabLayout">
                      <item row="0" column="0">
                       <widget class="QLabel" name="tscaleScalarLabel">
                        <property name="text">
                         <string>Scaler</string>
                        </property>
                       </widget>
                      </item>
                      <item row="0" column="1" colspan="3">
                       <widget class="QComboBox" name="tscaleScaler">
                        <item>
                         <property name="text">
                          <string>Oversample</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Linear</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline16</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline36</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Spline64</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Sinc</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Lanczos</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Ginseng</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bicubic</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Bc spline</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Catmull rom</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Mitchell</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Robidoux sharp</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Box</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Nearest</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Triangle</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Gaussian</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                      <item row="1" column="0">
                       <widget class="QLabel" name="tscaleParam1Label">
                        <property name="text">
                         <string>1</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="1">
                       <layout class="QHBoxLayout" name="tscaleParam1Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleParam1Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleParam1Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="1" column="2">
                       <widget class="QLabel" name="tscaleRadiusLabel">
                        <property name="text">
                         <string>Radius</string>
                        </property>
                       </widget>
                      </item>
                      <item row="1" column="3">
                       <layout class="QHBoxLayout" name="tscaleRadiusLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleRadiusSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleRadiusValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>16.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.500000000000000</double>
                          </property>
                          <property name="value">
                           <double>2.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="0">
                       <widget class="QLabel" name="tscaleParam2Label">
                        <property name="text">
                         <string>2</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="1">
                       <layout class="QHBoxLayout" name="tscaleParam2Layout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleParam2Set">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleParam2Value">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="2" column="2">
                       <widget class="QLabel" name="tscaleAntiRingLabel">
                        <property name="text">
                         <string>Anti-ring</string>
                        </property>
                       </widget>
                      </item>
                      <item row="2" column="3">
                       <layout class="QHBoxLayout" name="tscaleAntiRingLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleAntiRingSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleAntiRingValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="0">
                       <widget class="QLabel" name="tscaleBlurLabel">
                        <property name="text">
                         <string>Blur</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="1">
                       <layout class="QHBoxLayout" name="tscaleBlurLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleBlurSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleBlurValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="3" column="2">
                       <widget class="QLabel" name="tscaleClampLabel">
                        <property name="text">
                         <string>Clamp</string>
                        </property>
                       </widget>
                      </item>
                      <item row="3" column="3">
                       <layout class="QHBoxLayout" name="tscaleClampLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleClampSet"/>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleClampValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="0">
                       <widget class="QLabel" name="tscaleWindowLabel">
                        <property name="text">
                         <string>Window</string>
                        </property>
                       </widget>
                      </item>
                      <item row="4" column="1">
                       <layout class="QHBoxLayout" name="tscaleWindowLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleWindowSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QComboBox" name="tscaleWindowValue">
                          <item>
                           <property name="text">
                            <string>Box</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Triangle</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Bartlett</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hanning</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Hamming</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Quadric</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Welch</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Kaiser</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Blackman</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Gaussian</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Jinc</string>
                           </property>
                          </item>
                          <item>
                           <property name="text">
                            <string>Sphinx</string>
                           </property>
                          </item>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item row="4" column="2" colspan="2">
                       <layout class="QHBoxLayout" name="tscaleWindowParamLayout" stretch="0,1">
                        <item>
                         <widget class="QCheckBox" name="tscaleWindowParamSet">
                          <property name="text">
                           <string/>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="tscaleWindowParamValue">
                          <property name="decimals">
                           <number>6</number>
                          </property>
                          <property name="minimum">
                           <double>-100.000000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                     </layout>
                    </widget>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="debandTab">
                 <attribute name="title">
                  <string>Deband</string>
                 </attribute>
                 <layout class="QFormLayout" name="debandTabLayout">
                  <property name="fieldGrowthPolicy">
                   <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                  </property>
                  <item row="0" column="0">
                   <widget class="QLabel" name="debandStateLabel">
                    <property name="text">
                     <string>State</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <widget class="QCheckBox" name="debandEnabled">
                    <property name="text">
                     <string>Enabled</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="debandIterationsLabel">
                    <property name="text">
                     <string>Iterations</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QSpinBox" name="debandIterations">
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>16</number>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QLabel" name="debandThresholdLabel">
                    <property name="text">
                     <string>Threshold</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QDoubleSpinBox" name="debandThreshold">
                    <property name="decimals">
                     <number>6</number>
                    </property>
                    <property name="maximum">
                     <double>4096.000000000000000</double>
                    </property>
                    <property name="value">
                     <double>64.000000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QLabel" name="debandRangeLabel">
                    <property name="text">
                     <string>Range</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QDoubleSpinBox" name="debandRange">
                    <property name="decimals">
                     <number>6</number>
                    </property>
                    <property name="minimum">
                     <double>1.000000000000000</double>
                    </property>
                    <property name="maximum">
                     <double>64.000000000000000</double>
                    </property>
                    <property name="value">
                     <double>16.000000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="0">
                   <widget class="QLabel" name="debandGrainLabel">
                    <property name="text">
                     <string>Grain</string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="1">
                   <widget class="QDoubleSpinBox" name="debandGrain">
                    <property name="decimals">
                     <number>6</number>
                    </property>
                    <property name="maximum">
                     <double>4096.000000000000000</double>
                    </property>
                    <property name="value">
                     <double>48.000000000000000</double>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="ccTab">
                 <attribute name="title">
                  <string>Color Correction</string>
                 </attribute>
                 <layout class="QFormLayout" name="formLayout_3">
                  <property name="fieldGrowthPolicy">
                   <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                  </property>
                  <item row="3" column="0">
                   <widget class="QLabel" name="ccTargetPrimLabel">
                    <property name="text">
                     <string>Target Prim</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QComboBox" name="ccTargetPrim">
                    <item>
                     <property name="text">
                      <string>Auto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.601-525</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.601-625</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.709</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.2020</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.470M</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Apple</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Adobe</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ProPhoto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>CIE1931</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>DCI-P3</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>V-Gamut</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>S-Gamut</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>EBU3213</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Film-C</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ACES AP0</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ACES AP1</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="4" column="0">
                   <widget class="QLabel" name="ccTargetTRCLabel">
                    <property name="text">
                     <string>Target TRC</string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="1">
                   <widget class="QComboBox" name="ccTargetTrc_v2">
                    <item>
                     <property name="text">
                      <string>Auto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.1886</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>sRGB</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Linear</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gamma 1.8</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gamma 2.0</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gamma 2.2</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gamma 2.4</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gamma 2.6</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gamma 2.8</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ProPhoto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>PQ</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>HLG</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Panasonic V-Log</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Sony S-Log1</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Sony S-Log2</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ST 428</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="5" column="0">
                   <widget class="QLabel" name="label">
                    <property name="text">
                     <string>Target Peak</string>
                    </property>
                   </widget>
                  </item>
                  <item row="5" column="1">
                   <widget class="QSpinBox" name="ccTargetPeak">
                    <property name="specialValueText">
                     <string>Based on TRC</string>
                    </property>
                    <property name="minimum">
                     <number>9</number>
                    </property>
                    <property name="maximum">
                     <number>10000</number>
                    </property>
                    <property name="value">
                     <number>9</number>
                    </property>
                   </widget>
                  </item>
                  <item row="6" column="0">
                   <widget class="QLabel" name="ccHdrLabel">
                    <property name="text">
                     <string>HDR Tone mapping</string>
                    </property>
                   </widget>
                  </item>
                  <item row="6" column="1">
                   <layout class="QHBoxLayout" name="horizontalLayout_2">
                    <item>
                     <widget class="QComboBox" name="ccHdrMapper">
                      <property name="currentIndex">
                       <number>1</number>
                      </property>
                      <item>
                       <property name="text">
                        <string>Clip</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Mobius</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Reinhard</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Hable</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Gamma</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Linear</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                    <item>
                     <widget class="QStackedWidget" name="ccHdrStack">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="currentIndex">
                       <number>1</number>
                      </property>
                      <widget class="QWidget" name="ccHdrClipPage">
                       <layout class="QFormLayout" name="formLayout_2">
                        <property name="leftMargin">
                         <number>3</number>
                        </property>
                        <property name="topMargin">
                         <number>0</number>
                        </property>
                        <property name="rightMargin">
                         <number>0</number>
                        </property>
                        <property name="bottomMargin">
                         <number>0</number>
                        </property>
                        <item row="0" column="0">
                         <widget class="QLabel" name="ccHdrClipParamLabel">
                          <property name="text">
                           <string>Luma</string>
                          </property>
                         </widget>
                        </item>
                        <item row="0" column="1">
                         <widget class="QDoubleSpinBox" name="ccHdrClipParam">
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                          <property name="value">
                           <double>1.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                      <widget class="QWidget" name="ccHdrMobiusPage">
                       <layout class="QFormLayout" name="formLayout">
                        <property name="leftMargin">
                         <number>3</number>
                        </property>
                        <property name="topMargin">
                         <number>0</number>
                        </property>
                        <property name="rightMargin">
                         <number>0</number>
                        </property>
                        <property name="bottomMargin">
                         <number>0</number>
                        </property>
                        <item row="0" column="0">
                         <widget class="QLabel" name="ccHdrMobiusParamLabel">
                          <property name="text">
                           <string>Juncture</string>
                          </property>
                         </widget>
                        </item>
                        <item row="0" column="1">
                         <widget class="QDoubleSpinBox" name="ccHdrMobiusParam">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimum">
                           <double>0.300000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>1.000000000000000</double>
                          </property>
                          <property name="singleStep">
                           <double>0.100000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                      <widget class="QWidget" name="ccHdrReinhardPage">
                       <layout class="QFormLayout" name="ccReinhardPageLayout">
                        <property name="leftMargin">
                         <number>3</number>
                        </property>
                        <property name="topMargin">
                         <number>0</number>
                        </property>
                        <property name="rightMargin">
                         <number>0</number>
                        </property>
                        <property name="bottomMargin">
                         <number>0</number>
                        </property>
                        <item row="0" column="0">
                         <widget class="QLabel" name="ccHdrReinhardParamLabel">
                          <property name="text">
                           <string>Contrast</string>
                          </property>
                         </widget>
                        </item>
                        <item row="0" column="1">
                         <widget class="QDoubleSpinBox" name="ccHdrReinhardParam">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="value">
                           <double>0.500000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                      <widget class="QWidget" name="ccHablePage"/>
                      <widget class="QWidget" name="ccHdrGammaPage">
                       <layout class="QFormLayout" name="ccHdrGammaPageLayout">
                        <property name="leftMargin">
                         <number>3</number>
                        </property>
                        <property name="topMargin">
                         <number>0</number>
                        </property>
                        <property name="rightMargin">
                         <number>0</number>
                        </property>
                        <property name="bottomMargin">
                         <number>0</number>
                        </property>
                        <item row="0" column="0">
                         <widget class="QLabel" name="ccHdrGammaParamLabel">
                          <property name="text">
                           <string>Gamma</string>
                          </property>
                         </widget>
                        </item>
                        <item row="0" column="1">
                         <widget class="QDoubleSpinBox" name="ccHdrGammaParam">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimum">
                           <double>0.100000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>10.000000000000000</double>
                          </property>
                          <property name="value">
                           <double>1.800000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                      <widget class="QWidget" name="ccHdrLinearPage">
                       <layout class="QFormLayout" name="ccHdrLinearPageLayout">
                        <property name="leftMargin">
                         <number>3</number>
                        </property>
                        <property name="topMargin">
                         <number>0</number>
                        </property>
                        <property name="rightMargin">
                         <number>0</number>
                        </property>
                        <property name="bottomMargin">
                         <number>0</number>
                        </property>
                        <item row="0" column="0">
                         <widget class="QLabel" name="ccHdrLinearParamLabel">
                          <property name="text">
                           <string>Scale</string>
                          </property>
                         </widget>
                        </item>
                        <item row="0" column="1">
                         <widget class="QDoubleSpinBox" name="ccHdrLinearParam">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="decimals">
                           <number>2</number>
                          </property>
                          <property name="minimum">
                           <double>0.010000000000000</double>
                          </property>
                          <property name="maximum">
                           <double>100.000000000000000</double>
                          </property>
                          <property name="value">
                           <double>1.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item row="7" column="0">
                   <widget class="QLabel" name="ccHdrComputeLabel">
                    <property name="text">
                     <string>HDR Compute Peak</string>
                    </property>
                   </widget>
                  </item>
                  <item row="7" column="1">
                   <widget class="QComboBox" name="ccHdrCompute">
                    <property name="currentIndex">
                     <number>2</number>
                    </property>
                    <item>
                     <property name="text">
                      <string>Auto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Yes</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>No</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="8" column="0">
                   <widget class="QLabel" name="ccTargetICCLabel">
                    <property name="text">
                     <string>ICC Profile</string>
                    </property>
                   </widget>
                  </item>
                  <item row="8" column="1">
                   <layout class="QHBoxLayout" name="ccICCLayout" stretch="0,1,0">
                    <item>
                     <widget class="QCheckBox" name="ccICCAutodetect">
                      <property name="text">
                       <string>Autodetect</string>
                      </property>
                      <property name="checked">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QLineEdit" name="ccICCLocation">
                      <property name="enabled">
                       <bool>false</bool>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="ccICCBrowse">
                      <property name="enabled">
                       <bool>false</bool>
                      </property>
                      <property name="text">
                       <string>Browse</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item row="2" column="0">
                   <widget class="QLabel" name="ccTargetGamutLabel">
                    <property name="text">
                     <string>Target Gamut</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QComboBox" name="ccTargetGamut">
                    <item>
                     <property name="text">
                      <string>Auto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.601-525</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.601-625</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.709</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.2020</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>BT.470M</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Apple</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Adobe</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ProPhoto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>CIE1931</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>DCI-P3</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>V-Gamut</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>S-Gamut</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>EBU3213</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Film-C</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ACES AP0</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>ACES AP1</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="ccGamutMappingLabel">
                    <property name="text">
                     <string>Gamut mapping</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QComboBox" name="ccGamutMapping">
                    <item>
                     <property name="text">
                      <string>Auto</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Clip</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Perceptual</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Relative</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Saturation</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Absolute</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Desaturate</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Darken</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Warn</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Linear</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_4">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="shadersPage">
          <layout class="QVBoxLayout" name="shadersPageLayout">
           <item>
            <widget class="QLabel" name="shadersWikiLabel">
             <property name="text">
              <string>Shaders contain special effects which can be added to the video rendering process. A list of community-made shaders can be found on &lt;a href=&quot;https://github.com/mpv-player/mpv/wiki/User-Scripts#user-shaders&quot;&gt;&lt;span style=&quot; text-decoration: underline; color:#007af4;&quot;&gt;the mpv wiki&lt;/span&gt;&lt;/a&gt;.</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
             <property name="openExternalLinks">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTabWidget" name="shadersTabs">
             <property name="currentIndex">
              <number>0</number>
             </property>
             <widget class="QWidget" name="shadersFilesTab">
              <attribute name="title">
               <string>Filesystem</string>
              </attribute>
              <layout class="QHBoxLayout" name="shadersFilesTabLayout">
               <item>
                <layout class="QVBoxLayout" name="shadersFileListLayout">
                 <item>
                  <widget class="QListWidget" name="shadersFileList">
                   <property name="dragEnabled">
                    <bool>true</bool>
                   </property>
                   <property name="dragDropMode">
                    <enum>QAbstractItemView::DragDropMode::DragDrop</enum>
                   </property>
                   <property name="defaultDropAction">
                    <enum>Qt::DropAction::CopyAction</enum>
                   </property>
                   <property name="selectionMode">
                    <enum>QAbstractItemView::SelectionMode::ExtendedSelection</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="shadersFileListActionsLayout">
                   <item>
                    <widget class="QPushButton" name="shadersAddFile">
                     <property name="text">
                      <string>Add shader file(s)...</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="shadersRemoveFile">
                     <property name="text">
                      <string>Remove</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="shadersLayout">
                 <item>
                  <widget class="QPushButton" name="shadersAddToShaders">
                   <property name="text">
                    <string>Add to shaders</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QGroupBox" name="shadersPresetsBox">
                   <property name="title">
                    <string>Shader presets</string>
                   </property>
                   <layout class="QVBoxLayout" name="shadersPresetsBoxLayout">
                    <item>
                     <widget class="QComboBox" name="shadersPresetsList">
                      <property name="editable">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="shadersPresetsLoad">
                      <property name="text">
                       <string>Load</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="shadersPresetsSave">
                      <property name="text">
                       <string>Save</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="shadersPresetsDelete">
                      <property name="text">
                       <string>Delete</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                 </item>
                 <item>
                  <spacer name="shadersSpacer">
                   <property name="orientation">
                    <enum>Qt::Orientation::Vertical</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>20</width>
                     <height>40</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="shadersWikiTab">
              <attribute name="title">
               <string>Wiki</string>
              </attribute>
              <layout class="QHBoxLayout" name="shadersWikiTabLayout">
               <item>
                <widget class="QListWidget" name="shadersWikiList">
                 <property name="dragDropMode">
                  <enum>QAbstractItemView::DragDropMode::InternalMove</enum>
                 </property>
                 <property name="defaultDropAction">
                  <enum>Qt::DropAction::IgnoreAction</enum>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QVBoxLayout" name="shadersWikiLayout">
                 <item>
                  <widget class="QPushButton" name="shadersWikiAdd">
                   <property name="text">
                    <string>Add to shaders</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="shadersWikiSync">
                   <property name="text">
                    <string>Sync</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="shadersWikiSpacer">
                   <property name="orientation">
                    <enum>Qt::Orientation::Vertical</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>20</width>
                     <height>40</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="shadersActiveBox">
             <property name="title">
              <string>Active shaders</string>
             </property>
             <layout class="QHBoxLayout" name="shadersActiveBoxLayout">
              <item>
               <widget class="QListWidget" name="shadersActiveList">
                <property name="dragEnabled">
                 <bool>true</bool>
                </property>
                <property name="dragDropMode">
                 <enum>QAbstractItemView::DragDropMode::DragDrop</enum>
                </property>
                <property name="defaultDropAction">
                 <enum>Qt::DropAction::CopyAction</enum>
                </property>
                <property name="selectionMode">
                 <enum>QAbstractItemView::SelectionMode::ExtendedSelection</enum>
                </property>
               </widget>
              </item>
              <item>
               <layout class="QVBoxLayout" name="shadersActiveLayout">
                <item>
                 <widget class="QPushButton" name="shadersActiveRemove">
                  <property name="text">
                   <string>Remove</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="shadersActiveSpacer">
                  <property name="orientation">
                   <enum>Qt::Orientation::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>40</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="fullscreenPage">
          <layout class="QGridLayout" name="fullscreenPageLayout" columnstretch="1,0">
           <item row="0" column="1">
            <widget class="QGroupBox" name="fullscreenHidingBox">
             <property name="title">
              <string>Hiding</string>
             </property>
             <layout class="QGridLayout" name="fullscreenHidingBoxLayout">
              <item row="1" column="1">
               <widget class="QSpinBox" name="fullscreenShowWhenDuration">
                <property name="suffix">
                 <string notr="true"> ms</string>
                </property>
                <property name="maximum">
                 <number>5000</number>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QCheckBox" name="fullscreenHidePanels">
                <property name="text">
                 <string>Hide docked panels</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="0" colspan="2">
               <widget class="QCheckBox" name="fullscreenHideControls">
                <property name="text">
                 <string>Hide controls in fullscreen</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QComboBox" name="fullscreenShowWhen">
                <property name="currentIndex">
                 <number>2</number>
                </property>
                <item>
                 <property name="text">
                  <string>Never show</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Show when moving the cursor, hide after:</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Show when hovering control, hide after:</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QGroupBox" name="fullscreenMonitorBox">
             <property name="title">
              <string>Fullscreen monitor</string>
             </property>
             <layout class="QVBoxLayout" name="fullscreenMonitorBoxLayout">
              <item>
               <layout class="QHBoxLayout" name="fullscreenMonitorLayout"/>
              </item>
              <item>
               <widget class="QCheckBox" name="fullscreenLaunch">
                <property name="text">
                 <string>Launch files in fullscreen</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="fullscreenWindowedAtEnd">
                <property name="text">
                 <string>Exit fullscreen at the end of playback</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="0">
            <spacer name="verticalSpacer_6">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="syncPage">
          <layout class="QVBoxLayout" name="syncPageLayout">
           <item>
            <widget class="QGroupBox" name="framedroppingBox">
             <property name="title">
              <string>Framedropping</string>
             </property>
             <layout class="QFormLayout" name="framedroppingBoxLayout">
              <property name="fieldGrowthPolicy">
               <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="framedroppingModeLabel">
                <property name="text">
                 <string>Mode</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QComboBox" name="framedroppingMode">
                <property name="currentIndex">
                 <number>1</number>
                </property>
                <item>
                 <property name="text">
                  <string>No</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Video</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Decoder</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Decoder+Video</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="framedroppingDecoderModeLabel">
                <property name="text">
                 <string>Decoder</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="framedroppingDecoderMode">
                <property name="currentIndex">
                 <number>1</number>
                </property>
                <item>
                 <property name="text">
                  <string>None</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Default</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Non reference</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Bi-directional</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Non key</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>All</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="syncAudioVideoBox">
             <property name="title">
              <string>Audio/Video sync</string>
             </property>
             <layout class="QFormLayout" name="syncAudioVideoBoxLayout">
              <property name="fieldGrowthPolicy">
               <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="syncModeLabel">
                <property name="text">
                 <string>Mode</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QComboBox" name="syncMode">
                <item>
                 <property name="text">
                  <string>Audio</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Resample audio to match video</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Resample audio to match video (may drop frames)</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Drop or repeat video frames</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Drop or repeat audio data</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="syncAudioDropSizeLabel">
                <property name="text">
                 <string>Audio drop size</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="syncMaxAudioChangeLabel">
                <property name="text">
                 <string>Max audio change</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="syncMaxVideoChangeLabel">
                <property name="text">
                 <string>Max video change</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QDoubleSpinBox" name="syncAudioDropSize">
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
                </property>
                <property name="decimals">
                 <number>6</number>
                </property>
                <property name="maximum">
                 <double>1.000000000000000</double>
                </property>
                <property name="value">
                 <double>0.020000000000000</double>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QDoubleSpinBox" name="syncMaxAudioChange">
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
                </property>
                <property name="decimals">
                 <number>6</number>
                </property>
                <property name="maximum">
                 <double>1.000000000000000</double>
                </property>
                <property name="value">
                 <double>0.120000000000000</double>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QDoubleSpinBox" name="syncMaxVideoChange">
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
                </property>
                <property name="decimals">
                 <number>6</number>
                </property>
                <property name="maximum">
                 <double>1.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.000000000000000</double>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="syncPageSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="hwdecPage">
          <layout class="QVBoxLayout" name="hwdecPageLayout">
           <item>
            <widget class="QLabel" name="hwdecNotice">
             <property name="text">
              <string>Hardware decoding may produce a smoother, more efficient overall experience and reduce strain on your cpu. However, some of the listed codecs (depending upon your installed hardware and software) may not be available, may be broken, and may produce incorrect output. A safe and always correct rule of thumb with respect to image quality is to prefer software decoding if your cpu can handle it.</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="hwdecEnable">
             <property name="text">
              <string>Use hardware-accelerated decoding</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTabWidget" name="hwdecTabs">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="currentIndex">
              <number>1</number>
             </property>
             <widget class="QWidget" name="hwdecCodecTab">
              <attribute name="title">
               <string>Codecs to allow hardware decoding</string>
              </attribute>
              <layout class="QVBoxLayout" name="verticalLayout_3">
               <item>
                <widget class="QCheckBox" name="hwdecAll">
                 <property name="text">
                  <string>All</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="hwdecCodecs" native="true">
                 <layout class="QVBoxLayout" name="verticalLayout_2">
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QCheckBox" name="hwdecMJpeg">
                    <property name="text">
                     <string notr="true">Motion JPEG</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecMpeg1Video">
                    <property name="text">
                     <string notr="true">MPEG-1 Video</string>
                    </property>
                    <property name="checked">
                     <bool>false</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecMpeg2Video">
                    <property name="text">
                     <string notr="true">MPEG-2 Video</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecMpeg4">
                    <property name="text">
                     <string notr="true">MPEG-4 Part 2</string>
                    </property>
                    <property name="checked">
                     <bool>false</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecH263">
                    <property name="text">
                     <string notr="true">H.263</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecH264">
                    <property name="text">
                     <string notr="true">H.264</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecVc1">
                    <property name="text">
                     <string notr="true">SMPTE VC-1</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecWmv3">
                    <property name="text">
                     <string notr="true">Windows Media Video 9</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecHevc">
                    <property name="text">
                     <string notr="true">High Efficency Video Coding</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="hwdecVp9">
                    <property name="text">
                     <string notr="true">Google VP9</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="hwdecBackendTab">
              <attribute name="title">
               <string>Hardware decoding backend</string>
              </attribute>
              <layout class="QVBoxLayout" name="verticalLayout">
               <item>
                <widget class="QLabel" name="hwdecBackendNote">
                 <property name="text">
                  <string>Hover over each backend to display a description.</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendAuto">
                 <property name="toolTip">
                  <string>Autodetect - best of VAAPI, DXVA, D3D11VA, etc.</string>
                 </property>
                 <property name="text">
                  <string>A&amp;utodetect</string>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendVaapi">
                 <property name="toolTip">
                  <string>Linux - works with Intel and AMD GPUs through Mesa, and with nVidia through a translation layer; may only be correct in BT.601 and BT.709</string>
                 </property>
                 <property name="text">
                  <string notr="true">&amp;VAAPI</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendNvdec">
                 <property name="toolTip">
                  <string>nVidia only (faster than CUDA)</string>
                 </property>
                 <property name="text">
                  <string notr="true">&amp;NVDEC</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendVdpau">
                 <property name="toolTip">
                  <string>Linux - some gpus, does not always treat certain colorspaces like BT.2020 correctly</string>
                 </property>
                 <property name="text">
                  <string notr="true">V&amp;DPAU</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendDxva2">
                 <property name="toolTip">
                  <string>Windows - not safe; it appears to always use BT.601 for forced RGB conversion, but actual behavior depends on the GPU drivers</string>
                 </property>
                 <property name="text">
                  <string notr="true">D&amp;XVA2</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendD3d11va">
                 <property name="toolTip">
                  <string>Windows 8+ - usually safe but rounds 10 bit to 8 bit</string>
                 </property>
                 <property name="text">
                  <string notr="true">D&amp;3D11VA</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendCuda">
                 <property name="toolTip">
                  <string>nVidia only (likely 10x0+ only) - safe</string>
                 </property>
                 <property name="text">
                  <string notr="true">CUDA</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="hwdecBackendCrystalHd">
                 <property name="toolTip">
                  <string>PCI-E decoder card - safe</string>
                 </property>
                 <property name="text">
                  <string notr="true">Cr&amp;ystalHD</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="verticalSpacer">
                 <property name="orientation">
                  <enum>Qt::Orientation::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
           <item>
            <spacer name="hwdecSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="playlistPage">
          <layout class="QVBoxLayout" name="playlistPageLayout">
           <item>
            <widget class="QGroupBox" name="playbackPlayBox">
             <property name="title">
              <string>Playback progression</string>
             </property>
             <layout class="QVBoxLayout" name="playbackPlayBoxLayout">
              <item>
               <layout class="QHBoxLayout" name="playbackPlayLayout" stretch="0,0,1">
                <item>
                 <widget class="QRadioButton" name="playbackPlayTimes">
                  <property name="text">
                   <string>Pla&amp;y</string>
                  </property>
                  <property name="checked">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QSpinBox" name="playbackPlayAmount">
                  <property name="minimum">
                   <number>0</number>
                  </property>
                  <property name="maximum">
                   <number>1000</number>
                  </property>
                  <property name="value">
                   <number>1</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="playbackPlayTimesLabel">
                  <property name="text">
                   <string>times</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QRadioButton" name="playbackRepeatForever">
                <property name="text">
                 <string>Repeat fore&amp;ver</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="playbackLoopImages">
                <property name="text">
                 <string>Always loop images (manual progression)</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="playlistFormatBox">
             <property name="title">
              <string>Display Format</string>
             </property>
             <layout class="QVBoxLayout" name="playlistFormatBoxLayout">
              <item>
               <widget class="QLineEdit" name="playlistFormat">
                <property name="text">
                 <string notr="true">%artist{# - }{Unknown Artist - }{}%title{#}{$}{$}</string>
                </property>
                <property name="placeholderText">
                 <string notr="true">%artist{# - }{Unknown Artist - }{}%title{#}{$}{$}</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QTextBrowser" name="playlistFormatHelp">
                <property name="source">
                 <url>
                  <string>qrc:/text/playlistFormat.html</string>
                 </url>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="audioPage">
          <layout class="QGridLayout" name="audioPageLayout">
           <item row="0" column="0" colspan="2">
            <widget class="QGroupBox" name="audioBox">
             <property name="title">
              <string>Audio Renderer</string>
             </property>
             <layout class="QVBoxLayout" name="audioBoxLayout">
              <item>
               <layout class="QFormLayout" name="audioDeviceLayout">
                <property name="fieldGrowthPolicy">
                 <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="audioDeviceLabel">
                  <property name="text">
                   <string>Device</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QComboBox" name="audioDevice">
                  <item>
                   <property name="text">
                    <string>Auto</string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="audioChannelsLabel">
                  <property name="text">
                   <string>Channel layout</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QComboBox" name="audioChannels">
                  <property name="editable">
                   <bool>false</bool>
                  </property>
                  <item>
                   <property name="text">
                    <string>System layout (stereo fallback)</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Maximal layout (may output to unconnected channels)</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Stereo</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Audio switcher (WIP)</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QTabWidget" name="audioTabs">
                <property name="currentIndex">
                 <number>0</number>
                </property>
                <widget class="QWidget" name="allTab">
                 <attribute name="title">
                  <string>All</string>
                 </attribute>
                 <layout class="QGridLayout" name="allTabLayout" columnstretch="0,1,1">
                  <item row="1" column="1">
                   <widget class="QLineEdit" name="audioSpdifCodecs">
                    <property name="enabled">
                     <bool>false</bool>
                    </property>
                    <property name="text">
                     <string notr="true"/>
                    </property>
                    <property name="placeholderText">
                     <string notr="true">truehd,eac3,dts-hd,dts,ac3</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="0">
                   <widget class="QCheckBox" name="audioStreamSilence">
                    <property name="text">
                     <string>Stream silence (HDMI fixup)</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <widget class="QDoubleSpinBox" name="audioWaitTime">
                    <property name="suffix">
                     <string> seconds</string>
                    </property>
                    <property name="maximum">
                     <double>9.990000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QCheckBox" name="audioPitchCorrection">
                    <property name="text">
                     <string>Pitch correction</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QCheckBox" name="audioExclusiveMode">
                    <property name="text">
                     <string>Exclusive mode</string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="0">
                   <widget class="QCheckBox" name="audioNormalizeDownmix">
                    <property name="text">
                     <string>Normalize downmix</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QCheckBox" name="audioSpdif">
                    <property name="text">
                     <string>Use S/PDIF when available</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="2">
                   <spacer name="audioStreamSilenceSpacer">
                    <property name="orientation">
                     <enum>Qt::Orientation::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="pipewirePage">
                 <attribute name="title">
                  <string>Pipewire</string>
                 </attribute>
                 <layout class="QFormLayout" name="formLayout_8">
                  <item row="0" column="0">
                   <widget class="QLabel" name="pipewireBufferLabel">
                    <property name="text">
                     <string>Buffer</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <widget class="QSpinBox" name="pipewireBuffer">
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>2000</number>
                    </property>
                    <property name="value">
                     <number>20</number>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="pulsePage">
                 <attribute name="title">
                  <string>Pulse</string>
                 </attribute>
                 <layout class="QFormLayout" name="pulsePageLayout">
                  <property name="fieldGrowthPolicy">
                   <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                  </property>
                  <item row="0" column="0">
                   <widget class="QLabel" name="pulseBufferLabel">
                    <property name="text">
                     <string>Buffer</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <widget class="QSpinBox" name="pulseBuffer">
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>2000</number>
                    </property>
                    <property name="value">
                     <number>250</number>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="pulseLatencyLabel">
                    <property name="text">
                     <string>Fixes</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QCheckBox" name="pulseLatency">
                    <property name="text">
                     <string>Latency hacks</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="alsaPage">
                 <attribute name="title">
                  <string>Alsa</string>
                 </attribute>
                 <layout class="QVBoxLayout" name="alsaPageLAyout">
                  <item>
                   <widget class="QCheckBox" name="alsaResample">
                    <property name="text">
                     <string>Resample (may introduce latency)</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QCheckBox" name="alsaIgnoreChannelMap">
                    <property name="text">
                     <string>Ignore channel map (for specific alsa setups)</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="alsaPageSpacer">
                    <property name="orientation">
                     <enum>Qt::Orientation::Vertical</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>20</width>
                      <height>40</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="ossPage">
                 <attribute name="title">
                  <string>Oss</string>
                 </attribute>
                 <layout class="QFormLayout" name="ossPageLayout">
                  <property name="fieldGrowthPolicy">
                   <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                  </property>
                  <item row="0" column="0">
                   <widget class="QLabel" name="ossMixerDeviceLabel">
                    <property name="text">
                     <string>Mixer device</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <widget class="QLineEdit" name="ossMixerDevice">
                    <property name="text">
                     <string notr="true">/dev/mixer</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="ossMixerChannelLabel">
                    <property name="text">
                     <string>Mixer channel</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QLineEdit" name="ossMixerChannel">
                    <property name="text">
                     <string notr="true">pcm</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="jackPage">
                 <attribute name="title">
                  <string>Jack</string>
                 </attribute>
                 <layout class="QFormLayout" name="jackPageLayout">
                  <item row="2" column="0">
                   <widget class="QLabel" name="jackNameLabel">
                    <property name="text">
                     <string>Name</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QLineEdit" name="jackName">
                    <property name="text">
                     <string notr="true">mpc-qt</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QLabel" name="jackPortLabel">
                    <property name="text">
                     <string>Port</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QLineEdit" name="jackPort">
                    <property name="text">
                     <string notr="true"/>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QCheckBox" name="jackConnect">
                    <property name="text">
                     <string>Auto connect</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <widget class="QCheckBox" name="jackAutostart">
                    <property name="text">
                     <string>Start jackd at startup</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="0">
                   <widget class="QLabel" name="jackStartupLabel">
                    <property name="text">
                     <string>Startup</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QGroupBox" name="audioAutoloadBox">
             <property name="title">
              <string>Audio open settings</string>
             </property>
             <layout class="QFormLayout" name="audioAutoloadBoxLayout">
              <item row="0" column="0" colspan="2">
               <widget class="QCheckBox" name="audioAutoloadExternal">
                <property name="text">
                 <string>Auto-load external audio files</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="audioAutoloadPathLabel">
                <property name="text">
                 <string>Paths</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <layout class="QHBoxLayout" name="audioAutoloadPathLayout">
                <item>
                 <widget class="QLineEdit" name="audioAutoloadPath">
                  <property name="placeholderText">
                   <string notr="true">.;./audio</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="audioAutoloadPathReset">
                  <property name="text">
                   <string>Reset</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="audioAutoloadMatchLabel">
                <property name="text">
                 <string>Match</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QComboBox" name="audioAutoloadMatch">
                <property name="currentIndex">
                 <number>0</number>
                </property>
                <item>
                 <property name="text">
                  <string>Exact</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Fuzzy</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>All</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QGroupBox" name="audioBalanceBox">
             <property name="title">
              <string>Audio balance</string>
             </property>
             <layout class="QGridLayout" name="audioBalanceBoxLayout" columnstretch="1,1">
              <item row="0" column="0" colspan="2">
               <widget class="QScrollBar" name="audioBalance">
                <property name="cursor">
                 <cursorShape>PointingHandCursor</cursorShape>
                </property>
                <property name="minimum">
                 <number>-100</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="0" column="0">
            <spacer name="verticalSpacer_3">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="subtitlesPage">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <layout class="QVBoxLayout" name="subtitlesPageLayout" stretch="0,0">
           <item>
            <widget class="QGroupBox" name="subtitlePlacementBox">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="title">
              <string>Placement</string>
             </property>
             <layout class="QFormLayout" name="subtitlePlacementBoxLayout">
              <item row="0" column="0" colspan="2">
               <widget class="QCheckBox" name="subtitlesOverridePlacement">
                <property name="text">
                 <string>Override default subtitle placement</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="subtitlesPlacementLabel">
                <property name="text">
                 <string>Placement</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <layout class="QGridLayout" name="subtitlePlacementLayout" columnstretch="0,1,0,1">
                <item row="0" column="0">
                 <widget class="QLabel" name="subtitlePlacementXLabel">
                  <property name="text">
                   <string>X</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QComboBox" name="subtitlePlacementX">
                  <property name="currentIndex">
                   <number>1</number>
                  </property>
                  <item>
                   <property name="text">
                    <string>Left</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Center</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Right</string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item row="0" column="2">
                 <widget class="QLabel" name="subtitlePlacementYLabel">
                  <property name="text">
                   <string>Y</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="3">
                 <widget class="QComboBox" name="subtitlePlacementY">
                  <property name="currentIndex">
                   <number>2</number>
                  </property>
                  <item>
                   <property name="text">
                    <string>Top</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Center</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Bottom</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="subtitlesPositionLabel">
                <property name="text">
                 <string>Position</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QSpinBox" name="subtitlesPosition">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true">%</string>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>100</number>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QCheckBox" name="subtitlesUseMargins">
                <property name="text">
                 <string>Use margins</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="subtitlesOptionsBox">
             <property name="title">
              <string>Options</string>
             </property>
             <layout class="QFormLayout" name="subtitlesOptionsBoxLayout">
              <item row="0" column="0" colspan="2">
               <widget class="QCheckBox" name="subtitlesForceGrayscale">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>Force grayscale</string>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QLabel" name="subtitlesDelayStepLabel">
                <property name="text">
                 <string>Delay step</string>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QSpinBox" name="subtitlesDelayStep">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string notr="true"> ms</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>9999999</number>
                </property>
                <property name="value">
                 <number>500</number>
                </property>
               </widget>
              </item>
              <item row="1" column="0" colspan="2">
               <widget class="QCheckBox" name="subtitlesFixTiming">
                <property name="text">
                 <string>Fix timing</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QCheckBox" name="subtitlesClearOnSeek">
                <property name="text">
                 <string>Clear on seek</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="subtitlesAssOverrideLabel">
                <property name="text">
                 <string>ASS override</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QComboBox" name="subtitlesAssOverride">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <item>
                 <property name="text">
                  <string>No</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Yes</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Yes and only zoom signs</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Force</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="subsDefaultPage">
          <layout class="QVBoxLayout" name="verticalLayout_14">
           <item>
            <widget class="QCheckBox" name="subsAssoverride">
             <property name="text">
              <string extracomment="Might cause issues">ASS override</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="subsDefaultLayoutsBox">
             <property name="title">
              <string/>
             </property>
             <property name="flat">
              <bool>true</bool>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <layout class="QVBoxLayout" name="subsDefaultLayoutLeft" stretch="0,0">
                <item>
                 <widget class="QGroupBox" name="fontBox">
                  <property name="title">
                   <string>Font</string>
                  </property>
                  <layout class="QFormLayout" name="fontBoxLayout">
                   <item row="0" column="0" colspan="2">
                    <widget class="QFontComboBox" name="fontComboBox"/>
                   </item>
                   <item row="1" column="0">
                    <widget class="QLabel" name="fontStyleLabel">
                     <property name="text">
                      <string>Style</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="1">
                    <widget class="QCheckBox" name="fontBold">
                     <property name="text">
                      <string>Bold</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="1">
                    <widget class="QCheckBox" name="fontItalic">
                     <property name="text">
                      <string>Italic</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="0">
                    <widget class="QLabel" name="fontSizeLabel">
                     <property name="text">
                      <string>Size</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="1">
                    <widget class="QSpinBox" name="fontSize">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximum">
                      <number>9000</number>
                     </property>
                     <property name="value">
                      <number>55</number>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QGroupBox" name="borderBox">
                  <property name="title">
                   <string>Border style</string>
                  </property>
                  <layout class="QFormLayout" name="borderBoxLayout">
                   <property name="fieldGrowthPolicy">
                    <enum>QFormLayout::FieldGrowthPolicy::AllNonFixedFieldsGrow</enum>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="borderSizeLabel">
                     <property name="text">
                      <string>Border size</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QSpinBox" name="borderSize">
                     <property name="maximum">
                      <number>10</number>
                     </property>
                     <property name="value">
                      <number>3</number>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="subsDefaultLayoutRight">
                <item>
                 <widget class="QGroupBox" name="subsAlignmentBox">
                  <property name="title">
                   <string>Screen Alignment &amp;&amp; Margins</string>
                  </property>
                  <layout class="QVBoxLayout" name="subsAlignmentBoxLayout">
                   <item>
                    <layout class="QHBoxLayout" name="subsAlignmentMarginLayout">
                     <item>
                      <layout class="QGridLayout" name="subsAlignmentLayout">
                       <item row="0" column="0">
                        <widget class="QRadioButton" name="subsAlignmentTopLeft">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="0" column="1">
                        <widget class="QRadioButton" name="subsAlignmentTop">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="0" column="2">
                        <widget class="QRadioButton" name="subsAlignmentTopRight">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="1" column="0">
                        <widget class="QRadioButton" name="subsAlignmentLeft">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="1" column="1">
                        <widget class="QRadioButton" name="subsAlignmentCenter">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="1" column="2">
                        <widget class="QRadioButton" name="subsAlignmentRight">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="2" column="2">
                        <widget class="QRadioButton" name="subsAlignmentBottomRight">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="2" column="0">
                        <widget class="QRadioButton" name="subsAlignmentBottomLeft">
                         <property name="text">
                          <string/>
                         </property>
                        </widget>
                       </item>
                       <item row="2" column="1">
                        <widget class="QRadioButton" name="subsAlignmentBottom">
                         <property name="text">
                          <string/>
                         </property>
                         <property name="checked">
                          <bool>true</bool>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                     <item>
                      <spacer name="subsAlignmentSpacer">
                       <property name="orientation">
                        <enum>Qt::Orientation::Horizontal</enum>
                       </property>
                       <property name="sizeHint" stdset="0">
                        <size>
                         <width>0</width>
                         <height>0</height>
                        </size>
                       </property>
                      </spacer>
                     </item>
                     <item>
                      <layout class="QFormLayout" name="subsMarginLayout">
                       <item row="0" column="0">
                        <widget class="QLabel" name="subsMarginXLabel">
                         <property name="text">
                          <string>X</string>
                         </property>
                        </widget>
                       </item>
                       <item row="0" column="1">
                        <widget class="QSpinBox" name="subsMarginX"/>
                       </item>
                       <item row="1" column="0">
                        <widget class="QLabel" name="subsMarginYLabel">
                         <property name="text">
                          <string>Y</string>
                         </property>
                        </widget>
                       </item>
                       <item row="1" column="1">
                        <widget class="QSpinBox" name="subsMarginY">
                         <property name="value">
                          <number>20</number>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <widget class="QCheckBox" name="subsRelativeToVideoFrame">
                     <property name="text">
                      <string>Position subs relative to the video frame</string>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QCheckBox" name="subsAssRelativeToVideoFrame">
                     <property name="text">
                      <string>Position ASS subs relative to the video frame</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QGroupBox" name="subsColorsBox">
                  <property name="title">
                   <string>Colors</string>
                  </property>
                  <layout class="QFormLayout" name="subsColorsBoxLayout">
                   <item row="0" column="0">
                    <widget class="QLabel" name="subsColorLabel">
                     <property name="text">
                      <string>Color</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <layout class="QHBoxLayout" name="subsColorLayout">
                     <item>
                      <widget class="QLineEdit" name="subsColorValue">
                       <property name="inputMask">
                        <string>HHHHHH</string>
                       </property>
                       <property name="text">
                        <string notr="true">FFFF00</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="subsColorPick">
                       <property name="styleSheet">
                        <string notr="true">background: #FFFF00;</string>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <widget class="QLabel" name="subsBorderColorLabel">
                     <property name="text">
                      <string>Border color</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="1">
                    <layout class="QHBoxLayout" name="subsBorderColorLayout">
                     <item>
                      <widget class="QLineEdit" name="subsBorderColorValue">
                       <property name="inputMask">
                        <string>HHHHHH</string>
                       </property>
                       <property name="text">
                        <string notr="true">000000</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="subsBorderColorPick">
                       <property name="styleSheet">
                        <string notr="true">background: #000000;</string>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QGroupBox" name="subsShadowBox">
                  <property name="title">
                   <string>Shadow</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_2">
                   <item row="0" column="0" colspan="2">
                    <widget class="QCheckBox" name="subsShadowEnabled">
                     <property name="text">
                      <string>Enable shadow</string>
                     </property>
                     <property name="checked">
                      <bool>false</bool>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="0">
                    <widget class="QLabel" name="subsShadowColorLabel">
                     <property name="enabled">
                      <bool>false</bool>
                     </property>
                     <property name="text">
                      <string>Shadow color</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="1">
                    <layout class="QHBoxLayout" name="subsShadowColorLayout">
                     <item>
                      <widget class="QLineEdit" name="subsShadowColorValue">
                       <property name="enabled">
                        <bool>false</bool>
                       </property>
                       <property name="inputMask">
                        <string>HHHHHH</string>
                       </property>
                       <property name="text">
                        <string notr="true">000000</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="subsShadowColorPick">
                       <property name="enabled">
                        <bool>false</bool>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background: #000000;</string>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <widget class="QLabel" name="subsShadowOffsetLabel">
                     <property name="enabled">
                      <bool>false</bool>
                     </property>
                     <property name="text">
                      <string>Shadow offset</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="1">
                    <widget class="QSpinBox" name="subsShadowOffset">
                     <property name="enabled">
                      <bool>false</bool>
                     </property>
                     <property name="maximum">
                      <number>10</number>
                     </property>
                     <property name="value">
                      <number>1</number>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="subsMiscPage">
          <layout class="QVBoxLayout" name="subsMiscPageLayout">
           <item>
            <widget class="QCheckBox" name="subtitlesPreferDefaultForced_v3">
             <property name="text">
              <string>Prefer forced and/or default subtitle tracks</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="subtitlesPreferExternal">
             <property name="text">
              <string>Prefer external subtitles over embedded subtitles</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="subtitlesIgnoreEmbedded">
             <property name="text">
              <string>Ignore embedded subtitles</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="subtitlesAutoloadExternal">
             <property name="text">
              <string>Automatically load external subtitles</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="subtitlesRemoveSdh">
             <property name="text">
              <string>Remove subtitles additions for the deaf or hard-of-hearing (SDH)</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="subtitlesAutoloadBox">
             <property name="title">
              <string>Autoload paths</string>
             </property>
             <layout class="QFormLayout" name="subtitlesAutoloadBoxLayout">
              <item row="0" column="0">
               <widget class="QLabel" name="subtitlesAutoloadPathLabel">
                <property name="text">
                 <string>Path</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <layout class="QHBoxLayout" name="subtitlesAutoloadPathLayout">
                <item>
                 <widget class="QLineEdit" name="subtitlesAutoloadPath">
                  <property name="placeholderText">
                   <string notr="true">./subtitles;./subs</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="subtitlesAutoloadReset">
                  <property name="text">
                   <string>Reset</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="subtitlesAutoloadMatchLabel">
                <property name="text">
                 <string>Match</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="subtitlesAutoloadMatch">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <item>
                 <property name="text">
                  <string>Exact</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Fuzzy</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>All</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="subtitlesDatabaseBox">
             <property name="title">
              <string>Online database</string>
             </property>
             <layout class="QGridLayout" name="subtitlesDatabaseBoxLayout" columnstretch="0,0,0">
              <item row="1" column="0">
               <widget class="QLabel" name="subtitlesDatabaseTextLabel">
                <property name="text">
                 <string>https://</string>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QPushButton" name="subtitlesDatabaseTest">
                <property name="text">
                 <string>Test</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="subtitlesDatabaseLocation">
                <item>
                 <property name="text">
                  <string notr="true">www.opensubtitles.org/isdb</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="0" column="0" colspan="3">
               <widget class="QLabel" name="subtitlesDatabaseLocationLabel">
                <property name="text">
                 <string>Base url of the online subtitle database:</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="subsMiscPageSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="exportPage">
          <layout class="QVBoxLayout" name="exportPageLayout">
           <item>
            <widget class="QGroupBox" name="screenshotDirectoryBox">
             <property name="title">
              <string>Directories</string>
             </property>
             <layout class="QGridLayout" name="screenshotDirectoryBoxLayout">
              <item row="0" column="0">
               <widget class="QLabel" name="screenshotDirectoryLabel">
                <property name="text">
                 <string>Screenshot</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <layout class="QHBoxLayout" name="screenshotDirectoryLayout">
                <item>
                 <widget class="QCheckBox" name="screenshotDirectorySet">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="checked">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="screenshotDirectoryValue">
                  <property name="placeholderText">
                   <string notr="true">~/Pictures/mpc_shots</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="screenshotDirectoryBrowse">
                  <property name="text">
                   <string>...</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="screenshotEncodeLabel">
                <property name="text">
                 <string>Encode</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <layout class="QHBoxLayout" name="encodeDirectoryLayout">
                <item>
                 <widget class="QCheckBox" name="encodeDirectorySet">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="checked">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="encodeDirectoryValue">
                  <property name="placeholderText">
                   <string notr="true">~/Videos/mpc_encodes</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="encodeDirectoryBrowse">
                  <property name="text">
                   <string>...</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QLabel" name="screenshotDirectoryUsage">
                <property name="text">
                 <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Unchecked directories will place output files in the source directory. Streams do not have a source directory and will bring up a dialog when a field is left blank; &lt;span style=&quot; font-weight:600;&quot;&gt;Alt+I&lt;/span&gt; will always ask where to place files. Directories will be created if necessary; this may not work across all filesystems.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                </property>
                <property name="wordWrap">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="screenshotTemplateBox">
             <property name="title">
              <string>Filename template</string>
             </property>
             <layout class="QGridLayout" name="screenshotTemplateBoxLayout">
              <item row="0" column="0">
               <widget class="QLabel" name="screenshotTemplateLabel">
                <property name="text">
                 <string>Screenshot</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLineEdit" name="screenshotTemplate">
                <property name="placeholderText">
                 <string notr="true">%f_snapshot_%wP_[%t{yyyy.MM.dd_hh.mm.ss}]%s{_subs}</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="encodeTemplateLabel">
                <property name="text">
                 <string>Encode</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLineEdit" name="encodeTemplate">
                <property name="placeholderText">
                 <string notr="true">%f_encode_%aP-%bP_[%t{yyyy.MM.dd_hh.mm.ss}]%s{_subs}%d{_novideo}{_noaudio}</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QTextBrowser" name="screenshotEncodeTemplateHelp">
                <property name="source">
                 <url>
                  <string>qrc:/text/encodeFormat.html</string>
                 </url>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="encodingPage">
          <layout class="QVBoxLayout" name="verticalLayout_9">
           <item>
            <widget class="QTabWidget" name="encodingTabs">
             <property name="currentIndex">
              <number>0</number>
             </property>
             <widget class="QWidget" name="screenshotFormatTab">
              <attribute name="title">
               <string>Screenshot</string>
              </attribute>
              <layout class="QVBoxLayout" name="screenshotFormatTabLayout">
               <item>
                <widget class="QCheckBox" name="screenshotFormatHighBitDepth">
                 <property name="text">
                  <string>Save with high bit depth</string>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="screenshotFormat">
                 <item>
                  <property name="text">
                   <string notr="true">jpg</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string notr="true">png</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QStackedWidget" name="screenshotFormatStack">
                 <property name="currentIndex">
                  <number>0</number>
                 </property>
                 <widget class="QWidget" name="jpgPage">
                  <layout class="QFormLayout" name="jpgPageLayout">
                   <item row="0" column="1">
                    <layout class="QHBoxLayout" name="jpgQualityLayout">
                     <item>
                      <widget class="QSlider" name="jpgQuality">
                       <property name="cursor">
                        <cursorShape>PointingHandCursor</cursorShape>
                       </property>
                       <property name="maximum">
                        <number>100</number>
                       </property>
                       <property name="value">
                        <number>90</number>
                       </property>
                       <property name="orientation">
                        <enum>Qt::Orientation::Horizontal</enum>
                       </property>
                       <property name="tickPosition">
                        <enum>QSlider::TickPosition::TicksBothSides</enum>
                       </property>
                       <property name="tickInterval">
                        <number>5</number>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="jpgQualityValue">
                       <property name="text">
                        <string notr="true">090</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <widget class="QLabel" name="jpgSourceChromaLabel">
                     <property name="text">
                      <string>Source Chroma</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="1">
                    <layout class="QHBoxLayout" name="jpgSmoothLayout">
                     <item>
                      <widget class="QSlider" name="jpgSmooth">
                       <property name="cursor">
                        <cursorShape>PointingHandCursor</cursorShape>
                       </property>
                       <property name="maximum">
                        <number>100</number>
                       </property>
                       <property name="orientation">
                        <enum>Qt::Orientation::Horizontal</enum>
                       </property>
                       <property name="tickPosition">
                        <enum>QSlider::TickPosition::TicksBothSides</enum>
                       </property>
                       <property name="tickInterval">
                        <number>5</number>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="jpgSmoothValue">
                       <property name="text">
                        <string notr="true">000</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="jpgQualityLabel">
                     <property name="text">
                      <string>Quality</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="0">
                    <widget class="QLabel" name="jpgSmoothLabel">
                     <property name="text">
                      <string>Smooth</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="1">
                    <widget class="QCheckBox" name="jpgSourceChroma">
                     <property name="text">
                      <string>Yes</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="pngPage">
                  <layout class="QFormLayout" name="pngPageLayout">
                   <item row="0" column="0">
                    <widget class="QLabel" name="pngCompressionLabel">
                     <property name="text">
                      <string>Compression</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <layout class="QHBoxLayout" name="pngCompressionLayout">
                     <item>
                      <widget class="QSlider" name="pngCompression">
                       <property name="cursor">
                        <cursorShape>PointingHandCursor</cursorShape>
                       </property>
                       <property name="maximum">
                        <number>9</number>
                       </property>
                       <property name="value">
                        <number>7</number>
                       </property>
                       <property name="orientation">
                        <enum>Qt::Orientation::Horizontal</enum>
                       </property>
                       <property name="tickPosition">
                        <enum>QSlider::TickPosition::TicksBothSides</enum>
                       </property>
                       <property name="tickInterval">
                        <number>1</number>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="pngCompressionValue">
                       <property name="text">
                        <string notr="true">7</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <widget class="QLabel" name="pngFilterLabel">
                     <property name="text">
                      <string>Filter</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="1">
                    <layout class="QHBoxLayout" name="pngFilterLayout">
                     <item>
                      <widget class="QSlider" name="pngFilter">
                       <property name="cursor">
                        <cursorShape>PointingHandCursor</cursorShape>
                       </property>
                       <property name="maximum">
                        <number>5</number>
                       </property>
                       <property name="value">
                        <number>5</number>
                       </property>
                       <property name="orientation">
                        <enum>Qt::Orientation::Horizontal</enum>
                       </property>
                       <property name="tickPosition">
                        <enum>QSlider::TickPosition::TicksBothSides</enum>
                       </property>
                       <property name="tickInterval">
                        <number>1</number>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="pngFilterValue">
                       <property name="text">
                        <string notr="true">5</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <widget class="QLabel" name="pngColorspaceLabel">
                     <property name="text">
                      <string>Colorspace</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="1">
                    <widget class="QCheckBox" name="pngColorspace">
                     <property name="text">
                      <string>Include tag</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="encodeTab">
              <attribute name="title">
               <string>Encode</string>
              </attribute>
              <layout class="QGridLayout" name="encodeTabLayout">
               <item row="0" column="0" colspan="2">
                <widget class="QComboBox" name="encodeFormat">
                 <item>
                  <property name="text">
                   <string notr="true">WebM (VP8+OGG)</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string notr="true">MP4 (x264+MP3)</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QGroupBox" name="encodeVideoBox">
                 <property name="title">
                  <string>Video</string>
                 </property>
                 <layout class="QFormLayout" name="encodeVideoBoxLayout">
                  <item row="0" column="0" colspan="2">
                   <widget class="QCheckBox" name="encodeVideoForget">
                    <property name="text">
                     <string>Don't encode the video</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="0" colspan="2">
                   <widget class="QCheckBox" name="encodeVideoHardsub">
                    <property name="text">
                     <string>Hardsub</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QRadioButton" name="encodeVideoMethodFilesize">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="text">
                     <string>Filesi&amp;ze</string>
                    </property>
                    <property name="checked">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QDoubleSpinBox" name="encodeVideoFilesize">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="suffix">
                     <string>MB</string>
                    </property>
                    <property name="value">
                     <double>2.900000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QRadioButton" name="encodeVideoMethodBitrate">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="text">
                     <string>Bitrate</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QSpinBox" name="encodeVideoBitrate">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="suffix">
                     <string>kBits</string>
                    </property>
                    <property name="maximum">
                     <number>8192</number>
                    </property>
                    <property name="value">
                     <number>500</number>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="1">
                   <layout class="QFormLayout" name="encodeVideoFineLayout">
                    <property name="labelAlignment">
                     <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                    </property>
                    <item row="0" column="0">
                     <widget class="QCheckBox" name="encodeVideoCrf">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="text">
                       <string>crf</string>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="1">
                     <widget class="QSpinBox" name="encodeVideoCrfValue">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimum">
                       <number>-1</number>
                      </property>
                      <property name="maximum">
                       <number>63</number>
                      </property>
                      <property name="value">
                       <number>-1</number>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <widget class="QCheckBox" name="encodeVideoQMin">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="text">
                       <string>qmin</string>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="1">
                     <widget class="QSpinBox" name="encodeVideoQMinValue">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimum">
                       <number>-1</number>
                      </property>
                      <property name="maximum">
                       <number>69</number>
                      </property>
                      <property name="value">
                       <number>2</number>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="0">
                     <widget class="QCheckBox" name="encodeVideoQMax">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="text">
                       <string>qmax</string>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="1">
                     <widget class="QSpinBox" name="encodeVideoQMaxValue">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimum">
                       <number>-1</number>
                      </property>
                      <property name="maximum">
                       <number>1024</number>
                      </property>
                      <property name="value">
                       <number>31</number>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QGroupBox" name="encodeAudioBox">
                 <property name="title">
                  <string>Audio</string>
                 </property>
                 <layout class="QVBoxLayout" name="encodeAudioBoxLayout">
                  <item>
                   <widget class="QCheckBox" name="encodeAudioForget">
                    <property name="text">
                     <string>Don't encode the audio</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QSpinBox" name="encodeAudioBitrate">
                    <property name="suffix">
                     <string>kBits</string>
                    </property>
                    <property name="maximum">
                     <number>360</number>
                    </property>
                    <property name="singleStep">
                     <number>16</number>
                    </property>
                    <property name="value">
                     <number>96</number>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="encodeAudioSpacer">
                    <property name="orientation">
                     <enum>Qt::Orientation::Vertical</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>20</width>
                      <height>40</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </widget>
               </item>
               <item row="2" column="0" colspan="2">
                <widget class="QLabel" name="encodeIgnoreNotice">
                 <property name="text">
                  <string>&quot;Don't encode&quot; checkboxes are ignored for audio-only or video-only files.</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
           <item>
            <spacer name="encodingSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tweaksPage">
          <layout class="QFormLayout" name="tweaksPageLayout">
           <item row="0" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksFastSeek">
             <property name="toolTip">
              <string>Seek to keyframe when hardware decoding is unavailable</string>
             </property>
             <property name="text">
              <string>Prioritize seeking speed over accuracy</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksSeekFramedrop">
             <property name="text">
              <string>Drop frames before the seek target in the decoder</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksShowChapterMarks">
             <property name="text">
              <string>Show chapter marks in seek bar</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksOpenNextFile">
             <property name="text">
              <string>Use next/previous file in folder when there is only one item in the playlist</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksVolumeLimit">
             <property name="text">
              <string>Limit volume to 100% like mpc-hc</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksTimeShort">
             <property name="text">
              <string>Shorten the playback time indicator like mpc-hc</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksPreferWayland">
             <property name="text">
              <string>Prefer Wayland over XWayland (restart required)</string>
             </property>
            </widget>
           </item>
           <item row="7" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksMpvMouseEvents">
             <property name="text">
              <string>Send mouse events to mpv</string>
             </property>
            </widget>
           </item>
           <item row="8" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksMpvKeyEvents">
             <property name="text">
              <string>Send key events to mpv</string>
             </property>
            </widget>
           </item>
           <item row="9" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksVideoPreview">
             <property name="text">
              <string>Show video preview</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="10" column="0">
            <layout class="QHBoxLayout" name="tweaksTimeTooltipLayout" stretch="1">
             <item>
              <widget class="QCheckBox" name="tweaksTimeTooltip">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Show time tooltip:</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="10" column="1">
            <widget class="QComboBox" name="tweaksTimeTooltipLocation">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <item>
              <property name="text">
               <string>Above seekbar</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>Below seekbar</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="11" column="0" colspan="2">
            <widget class="QCheckBox" name="tweaksOsdTimerOnSeek">
             <property name="text">
              <string>Show OSD timer on seek</string>
             </property>
            </widget>
           </item>
           <item row="12" column="0">
            <layout class="QHBoxLayout" name="tweaksOsdFontLayout" stretch="1">
             <item>
              <widget class="QCheckBox" name="tweaksOsdFontChkBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Change OSD font:</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="12" column="1">
            <layout class="QHBoxLayout" name="tweaksOsdLayout" stretch="1,0">
             <item>
              <widget class="QFontComboBox" name="tweaksOsdFont">
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QSpinBox" name="tweaksOsdSize">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>9000</number>
               </property>
               <property name="value">
                <number>55</number>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="13" column="0">
            <layout class="QHBoxLayout" name="tweaksMpvOptionsLayout" stretch="1">
             <item>
              <widget class="QCheckBox" name="tweaksMpvOptionsChkBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Custom mpv options:</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="13" column="1">
            <layout class="QHBoxLayout" name="tweaksMpvOptionsTextLayout" stretch="1">
             <item>
              <widget class="QLineEdit" name="tweaksMpvOptionsText">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="placeholderText">
                <string notr="true">hwdec=auto interpolation=yes</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="loggingPage">
          <layout class="QGridLayout" name="gridLayout" rowstretch="0,0,0,0,1" columnstretch="1,1">
           <item row="0" column="0">
            <widget class="QCheckBox" name="loggingEnabled">
             <property name="text">
              <string>Turn on logging (may produce stuttering)</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="checked">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0" colspan="2">
            <widget class="QGroupBox" name="debugBox">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>Debugging</string>
             </property>
             <layout class="QFormLayout" name="formLayout_7">
              <item row="0" column="0">
               <widget class="QLabel" name="debugClientLabel">
                <property name="text">
                 <string>Client</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QCheckBox" name="debugClient">
                <property name="text">
                 <string>Event messages</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="debugMpvLabel">
                <property name="text">
                 <string>mpv</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="debugMpv">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="currentIndex">
                 <number>3</number>
                </property>
                <item>
                 <property name="text">
                  <string>No (complete silence)</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Fatal</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Error</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Warning</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Informational</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Verbose</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Debug</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>Trace (very noisy)</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="2" column="0" colspan="2">
            <widget class="QGroupBox" name="logFileBox">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>Log file</string>
             </property>
             <layout class="QFormLayout" name="formLayout_4">
              <item row="1" column="0" colspan="2">
               <widget class="QCheckBox" name="logFileCreate">
                <property name="text">
                 <string>Create log file (contents will be overwritten)</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="logFilePathLabel">
                <property name="text">
                 <string>Path</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <layout class="QHBoxLayout" name="logFilePathLayout">
                <item>
                 <widget class="QLineEdit" name="logFilePathValue">
                  <property name="placeholderText">
                   <string>~/mpc-qt-log.txt</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="logFilePathBrowse">
                  <property name="text">
                   <string>...</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QGroupBox" name="logUpdateBox">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>Update interval</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_7">
              <item>
               <widget class="QRadioButton" name="logUpdateNoDelay">
                <property name="text">
                 <string>No delay (consumes cpu, use this for testing)</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="logUpdateDelayed">
                <property name="text">
                 <string>Dela&amp;yed</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSpinBox" name="logUpdateInterval">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string> ms</string>
                </property>
                <property name="prefix">
                 <string>Every </string>
                </property>
                <property name="minimum">
                 <number>100</number>
                </property>
                <property name="maximum">
                 <number>10000</number>
                </property>
                <property name="value">
                 <number>2000</number>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QGroupBox" name="logHistoryBox">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>Window history</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_8">
              <item>
               <widget class="QRadioButton" name="logHistoryUnlimited">
                <property name="text">
                 <string>Unlimited (consumes &amp;memory)</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="logHistoryTrim">
                <property name="text">
                 <string>Trim old lines</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSpinBox" name="logHistoryLines">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="suffix">
                 <string> lines</string>
                </property>
                <property name="prefix">
                 <string>Keep </string>
                </property>
                <property name="minimum">
                 <number>100</number>
                </property>
                <property name="maximum">
                 <number>10000</number>
                </property>
                <property name="value">
                 <number>1000</number>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="4" column="0">
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="miscPage">
          <layout class="QVBoxLayout" name="miscPageLayout">
           <item>
            <widget class="QGroupBox" name="miscColorBox">
             <property name="title">
              <string>Color controls</string>
             </property>
             <layout class="QVBoxLayout" name="miscColorBoxLayout">
              <item>
               <layout class="QGridLayout" name="miscBchsBox" columnstretch="0,0,1">
                <item row="0" column="0">
                 <widget class="QLabel" name="miscBrightnessLabel">
                  <property name="text">
                   <string>Brightness</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QLabel" name="miscBrightnessValue">
                  <property name="minimumSize">
                   <size>
                    <width>30</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="text">
                   <string notr="true">   0</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="2">
                 <widget class="QScrollBar" name="miscBrightness">
                  <property name="minimum">
                   <number>-100</number>
                  </property>
                  <property name="maximum">
                   <number>100</number>
                  </property>
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="miscContrastLabel">
                  <property name="text">
                   <string>Contrast</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QLabel" name="miscContrastValue">
                  <property name="minimumSize">
                   <size>
                    <width>30</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="text">
                   <string notr="true">   0</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="2">
                 <widget class="QScrollBar" name="miscContrast">
                  <property name="minimum">
                   <number>-100</number>
                  </property>
                  <property name="maximum">
                   <number>100</number>
                  </property>
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QLabel" name="miscGammaLabel">
                  <property name="text">
                   <string>Gamma</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="1">
                 <widget class="QLabel" name="miscGammaValue">
                  <property name="minimumSize">
                   <size>
                    <width>30</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="text">
                   <string notr="true">   0</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="2">
                 <widget class="QScrollBar" name="miscGamma">
                  <property name="minimum">
                   <number>-100</number>
                  </property>
                  <property name="maximum">
                   <number>100</number>
                  </property>
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QLabel" name="miscHueLabel">
                  <property name="text">
                   <string>Hue</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="1">
                 <widget class="QLabel" name="miscHueValue">
                  <property name="minimumSize">
                   <size>
                    <width>30</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="text">
                   <string notr="true">   0</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="2">
                 <widget class="QScrollBar" name="miscHue">
                  <property name="minimum">
                   <number>-100</number>
                  </property>
                  <property name="maximum">
                   <number>100</number>
                  </property>
                  <property name="value">
                   <number>0</number>
                  </property>
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                 </widget>
                </item>
                <item row="4" column="0">
                 <widget class="QLabel" name="miscSaturationLabel">
                  <property name="text">
                   <string>Saturation</string>
                  </property>
                 </widget>
                </item>
                <item row="4" column="1">
                 <widget class="QLabel" name="miscSaturationValue">
                  <property name="minimumSize">
                   <size>
                    <width>30</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="text">
                   <string notr="true">   0</string>
                  </property>
                 </widget>
                </item>
                <item row="4" column="2">
                 <widget class="QScrollBar" name="miscSaturation">
                  <property name="minimum">
                   <number>-100</number>
                  </property>
                  <property name="maximum">
                   <number>100</number>
                  </property>
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="miscResetColorBox">
                <item>
                 <spacer name="miscResetColorSpacer">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QPushButton" name="miscResetColor">
                  <property name="text">
                   <string>Reset</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="miscSettingsBox">
             <property name="title">
              <string>Settings management</string>
             </property>
             <layout class="QHBoxLayout" name="miscSettingsBoxLayout">
              <item>
               <widget class="QPushButton" name="miscResetSettings">
                <property name="text">
                 <string>Reset</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="miscExportKeys">
                <property name="text">
                 <string>Export keys</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="miscExportSettings">
                <property name="text">
                 <string>Export</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="miscSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::StandardButton::Apply|QDialogButtonBox::StandardButton::Cancel|QDialogButtonBox::StandardButton::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>hwdecEnable</sender>
   <signal>toggled(bool)</signal>
   <receiver>hwdecTabs</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>258</x>
     <y>54</y>
    </hint>
    <hint type="destinationlabel">
     <x>258</x>
     <y>60</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>hwdecAll</sender>
   <signal>toggled(bool)</signal>
   <receiver>hwdecCodecs</receiver>
   <slot>setDisabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>264</x>
     <y>72</y>
    </hint>
    <hint type="destinationlabel">
     <x>264</x>
     <y>84</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>subtitlesAutoloadExternal</sender>
   <signal>toggled(bool)</signal>
   <receiver>subtitlesAutoloadBox</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>339</x>
     <y>168</y>
    </hint>
    <hint type="destinationlabel">
     <x>340</x>
     <y>178</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>webServePages</sender>
   <signal>toggled(bool)</signal>
   <receiver>webLocalFilesOptions</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>710</x>
     <y>278</y>
    </hint>
    <hint type="destinationlabel">
     <x>537</x>
     <y>291</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>webEnableServer</sender>
   <signal>toggled(bool)</signal>
   <receiver>webTcpIpOptions</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>537</x>
     <y>100</y>
    </hint>
    <hint type="destinationlabel">
     <x>537</x>
     <y>149</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
