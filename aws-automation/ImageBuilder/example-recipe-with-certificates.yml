# AWS ImageBuilder Recipe Example: Windows Server 2022 with Certificates
# Author: <PERSON><PERSON>
# Date: 2025-01-08
# Description: Example recipe showing how to include certificate installation

name: windows-server-2022-base-with-certificates
description: Base Windows Server 2022 AMI with trusted root certificates (e.g., LawTrust)
schemaVersion: 1.0
version: 1.0.0

components:
  # Step 1: Copy applications from S3 (includes installers needed by other components)
  - name: windows-applications-s3
    parameters: []

  # Step 2: Install trusted root certificates (LawTrust, etc.)
  - name: windows-certificates-install
    parameters:
      - name: S3Bucket
        value: 'sgt-imagebuilder'
      - name: S3Prefix
        value: 'windows/certificates/'
      - name: Region
        value: 'af-south-1'
      - name: CertificateStore
        value: 'Root'  # Trusted Root Certification Authorities

  # Step 3: Install CrowdStrike Falcon Sensor
  - name: windows-application-crowdstrike
    parameters:
      - name: SecretsManagerSecretName
        value: '/imagebuilder/crowdstrike/credentials'
      - name: Region
        value: 'af-south-1'

  # Step 4: Configure base firewall rules
  - name: windows-firewall-base
    parameters: []

  # Step 5: Configure registry settings for security
  - name: windows-registry-security
    parameters: []

  # Step 6: Configure registry settings for performance
  - name: windows-registry-performance
    parameters: []

  # Step 7: Configure event log settings
  - name: windows-registry-eventlog
    parameters: []

  # Step 8: Configure Windows Update settings
  - name: windows-registry-windowsupdate
    parameters: []

  # Step 9: Configure IE Enhanced Security
  - name: configure-registry-iesecurity
    parameters: []

