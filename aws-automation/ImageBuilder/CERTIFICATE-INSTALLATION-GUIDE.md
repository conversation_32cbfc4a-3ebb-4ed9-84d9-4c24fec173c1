# Certificate Installation Guide for AWS ImageBuilder

## Overview
This guide explains how to add trusted root certificates (like LawTrust) to your base AMI using AWS ImageBuilder.

## Certificate Format: DER vs Base64

### ✅ Recommended: DER Format (.cer or .crt)
- **Binary-encoded certificate**
- Native Windows support
- Works directly with `certutil` and `Import-Certificate`
- Smaller file size
- No conversion needed

### ❌ Not Recommended: Base64/PEM Format (.pem)
- Text-encoded certificate
- Requires conversion or additional handling
- Larger file size
- More prone to encoding issues

## Quick Start

### Step 1: Upload Certificate to S3

```powershell
# Upload your DER-format certificate to S3
aws s3 cp LawTrust.cer s3://sgt-imagebuilder/windows/certificates/ --region af-south-1

# For multiple certificates
aws s3 cp LawTrust.cer s3://sgt-imagebuilder/windows/certificates/
aws s3 cp AnotherCert.cer s3://sgt-imagebuilder/windows/certificates/
aws s3 cp ThirdCert.crt s3://sgt-imagebuilder/windows/certificates/
```

**Note:** The component supports both `.cer` and `.crt` extensions.

### Step 2: Deploy the ImageBuilder Component

```powershell
# Navigate to the ImageBuilder directory
cd AWS-EC2Deployment/ImageBuilder

# Deploy the component (first time)
.\deploy-certificates-component.ps1

# Update existing component with new version
.\deploy-certificates-component.ps1 -ForceUpdate

# Test without making changes
.\deploy-certificates-component.ps1 -DryRun
```

### Step 3: Add Component to Your Recipe

Add this to your ImageBuilder recipe YAML file:

```yaml
components:
  # ... other components ...
  
  - name: windows-certificates-install
    parameters:
      - name: S3Bucket
        value: 'sgt-imagebuilder'
      - name: S3Prefix
        value: 'windows/certificates/'
      - name: Region
        value: 'af-south-1'
      - name: CertificateStore
        value: 'Root'  # Trusted Root Certification Authorities
```

## Certificate Store Options

| Store Name | Description | Common Use Case |
|------------|-------------|-----------------|
| `Root` | Trusted Root Certification Authorities | **Most common** - For root CA certificates like LawTrust |
| `CA` | Intermediate Certification Authorities | For intermediate CA certificates |
| `TrustedPublisher` | Trusted Publishers | For code signing certificates |
| `AuthRoot` | Third-Party Root Certification Authorities | For third-party root CAs |
| `My` | Personal | For client certificates (less common for base AMI) |

**Recommendation:** Use `Root` for LawTrust and similar root CA certificates.

## Converting Base64 to DER (If Needed)

If you only have the Base64/PEM format, convert it to DER:

### Using OpenSSL (Linux/Mac/WSL)
```bash
openssl x509 -in certificate.pem -outform DER -out certificate.cer
```

### Using PowerShell (Windows)
```powershell
# Read Base64 certificate
$base64Cert = Get-Content -Path "certificate.pem" -Raw
$base64Cert = $base64Cert -replace "-----BEGIN CERTIFICATE-----", ""
$base64Cert = $base64Cert -replace "-----END CERTIFICATE-----", ""
$base64Cert = $base64Cert -replace "`n", "" -replace "`r", ""

# Convert to DER
$certBytes = [Convert]::FromBase64String($base64Cert)
[System.IO.File]::WriteAllBytes("certificate.cer", $certBytes)
```

### Using certutil (Windows)
```cmd
certutil -decode certificate.pem certificate.cer
```

## Adding Multiple Certificates

The component automatically installs **all** `.cer` and `.crt` files found in the S3 prefix. Simply upload multiple certificates:

```powershell
# Upload multiple certificates
aws s3 sync ./certificates/ s3://sgt-imagebuilder/windows/certificates/ --region af-south-1

# Or individually
aws s3 cp LawTrust.cer s3://sgt-imagebuilder/windows/certificates/
aws s3 cp InternalCA.cer s3://sgt-imagebuilder/windows/certificates/
aws s3 cp PartnerCA.crt s3://sgt-imagebuilder/windows/certificates/
```

All certificates will be installed to the same certificate store specified in the recipe.

## Organizing Multiple Certificate Types

If you need to install certificates to different stores, you can:

### Option 1: Use Subdirectories and Multiple Component Instances

```yaml
components:
  # Root CA certificates
  - name: windows-certificates-install
    parameters:
      - name: S3Prefix
        value: 'windows/certificates/root/'
      - name: CertificateStore
        value: 'Root'
  
  # Intermediate CA certificates
  - name: windows-certificates-install
    parameters:
      - name: S3Prefix
        value: 'windows/certificates/intermediate/'
      - name: CertificateStore
        value: 'CA'
```

S3 structure:
```
s3://sgt-imagebuilder/
  └── windows/
      └── certificates/
          ├── root/
          │   ├── LawTrust.cer
          │   └── InternalRootCA.cer
          └── intermediate/
              └── InternalIntermediateCA.cer
```

### Option 2: Single Directory (Simpler)

If all certificates go to the same store (most common), just use one directory:

```
s3://sgt-imagebuilder/
  └── windows/
      └── certificates/
          ├── LawTrust.cer
          ├── InternalCA.cer
          └── PartnerCA.cer
```

## Verification

After the AMI is built, you can verify certificate installation:

### Check Installed Certificates
```powershell
# List all certificates in Root store
Get-ChildItem -Path Cert:\LocalMachine\Root

# Find specific certificate by subject
Get-ChildItem -Path Cert:\LocalMachine\Root | Where-Object { $_.Subject -like "*LawTrust*" }

# Find by thumbprint
Get-ChildItem -Path Cert:\LocalMachine\Root | Where-Object { $_.Thumbprint -eq "YOUR_THUMBPRINT" }
```

### Check Installation Log
The component creates a log file during installation:
```powershell
Get-Content C:\Temp\ServerInstalls\Logs\Certificate-Install.log
```

## Troubleshooting

### Issue: "No certificate files found"
**Solution:** Ensure certificates are uploaded to the correct S3 path and have `.cer` or `.crt` extension.

```powershell
# Verify S3 contents
aws s3 ls s3://sgt-imagebuilder/windows/certificates/ --region af-south-1
```

### Issue: "S3 sync failed"
**Solution:** Check ImageBuilder instance profile has S3 read permissions.

Required IAM policy:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::sgt-imagebuilder",
        "arn:aws:s3:::sgt-imagebuilder/*"
      ]
    }
  ]
}
```

### Issue: "Certificate already installed (skipping)"
**Solution:** This is normal behavior. The component skips certificates that are already in the store.

### Issue: Certificate not trusted after installation
**Solution:** Verify you're installing to the correct store:
- Root CA certificates → `Root` store
- Intermediate CA certificates → `CA` store

## Component Features

✅ **Automatic duplicate detection** - Skips already-installed certificates  
✅ **Multiple certificate support** - Installs all certificates in S3 prefix  
✅ **Fallback installation** - Uses `certutil` if `Import-Certificate` fails  
✅ **Detailed logging** - Creates installation log for troubleshooting  
✅ **Validation phase** - Verifies all certificates are properly installed  
✅ **Both .cer and .crt support** - Accepts common DER file extensions  

## Example: Complete Recipe with Certificates

```yaml
name: windows-server-2022-base-with-certificates
description: Base Windows Server 2022 AMI with trusted certificates
schemaVersion: 1.0
version: 1.0.0

components:
  # Copy applications from S3
  - name: windows-applications-s3
    parameters: []
  
  # Install trusted root certificates
  - name: windows-certificates-install
    parameters:
      - name: S3Bucket
        value: 'sgt-imagebuilder'
      - name: S3Prefix
        value: 'windows/certificates/'
      - name: CertificateStore
        value: 'Root'
  
  # Install CrowdStrike
  - name: windows-application-crowdstrike
    parameters:
      - name: SecretsManagerSecretName
        value: '/imagebuilder/crowdstrike/credentials'
  
  # Configure firewall
  - name: windows-firewall-base
    parameters: []
```

## Best Practices

1. **Use DER format** - Always convert Base64/PEM to DER before uploading
2. **Organize by store** - Use subdirectories if installing to multiple stores
3. **Verify certificates** - Check certificate details before uploading:
   ```powershell
   $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
   $cert.Import("LawTrust.cer")
   $cert | Format-List Subject, Issuer, Thumbprint, NotBefore, NotAfter
   ```
4. **Test in non-production** - Build a test AMI first to verify certificate installation
5. **Document certificates** - Keep a record of which certificates are installed and why
6. **Monitor expiration** - Set reminders to update certificates before they expire

## Support

For issues or questions:
1. Check the installation log: `C:\Temp\ServerInstalls\Logs\Certificate-Install.log`
2. Review ImageBuilder build logs in AWS Console
3. Verify S3 permissions and file locations
4. Check certificate format (must be DER, not Base64/PEM)

