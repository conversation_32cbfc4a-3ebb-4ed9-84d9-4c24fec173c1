# Certificate Installation for ImageBuilder - Quick Start

## TL;DR - Quick Commands

```powershell
# 1. Upload your DER certificate to S3
.\upload-certificates-to-s3.ps1 -CertificatePath "C:\path\to\LawTrust.cer"

# 2. Deploy the ImageBuilder component
.\deploy-certificates-component.ps1

# 3. Add to your recipe (see example-recipe-with-certificates.yml)
```

## What You Need

1. **Certificate file in DER format** (`.cer` or `.crt` extension)
   - If you have Base64/PEM format, see conversion instructions below
2. **AWS CLI configured** with appropriate credentials
3. **S3 bucket access** (default: `sgt-imagebuilder`)

## Certificate Format: DER vs Base64

### ✅ Use DER Format (.cer or .crt)
- **This is what you want!**
- Binary-encoded certificate
- Works directly with Windows
- Smaller file size

### ❌ Don't Use Base64/PEM Format (.pem)
- Text-encoded certificate
- Requires conversion
- Larger file size

### How to Tell Which Format You Have

**DER format** - Binary file, looks like gibberish if you open it in Notepad:
```
0‚...0‚...¢...0...
```

**Base64/PEM format** - Text file, starts with:
```
-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKJ...
-----END CERTIFICATE-----
```

### Converting Base64 to DER

If you have Base64/PEM format, convert it first:

**Using PowerShell:**
```powershell
# Read Base64 certificate
$base64Cert = Get-Content -Path "certificate.pem" -Raw
$base64Cert = $base64Cert -replace "-----BEGIN CERTIFICATE-----", ""
$base64Cert = $base64Cert -replace "-----END CERTIFICATE-----", ""
$base64Cert = $base64Cert -replace "`n", "" -replace "`r", ""

# Convert to DER
$certBytes = [Convert]::FromBase64String($base64Cert)
[System.IO.File]::WriteAllBytes("certificate.cer", $certBytes)
```

**Using certutil (Windows):**
```cmd
certutil -decode certificate.pem certificate.cer
```

**Using OpenSSL (Linux/Mac/WSL):**
```bash
openssl x509 -in certificate.pem -outform DER -out certificate.cer
```

## Step-by-Step Guide

### Step 1: Upload Certificate(s) to S3

**Single certificate:**
```powershell
.\upload-certificates-to-s3.ps1 -CertificatePath "C:\Certificates\LawTrust.cer"
```

**Multiple certificates from a directory:**
```powershell
.\upload-certificates-to-s3.ps1 -CertificatePath "C:\Certificates\"
```

**Upload without validation (not recommended):**
```powershell
.\upload-certificates-to-s3.ps1 -CertificatePath "C:\Certificates\LawTrust.cer" -Validate:$false
```

**Dry run (test without uploading):**
```powershell
.\upload-certificates-to-s3.ps1 -CertificatePath "C:\Certificates\LawTrust.cer" -DryRun
```

The script will:
- ✅ Validate the certificate file
- ✅ Show certificate details (subject, issuer, expiration)
- ✅ Upload to S3
- ✅ List all certificates in S3

### Step 2: Deploy the ImageBuilder Component

```powershell
# First time deployment
.\deploy-certificates-component.ps1

# Update existing component
.\deploy-certificates-component.ps1 -ForceUpdate

# Test without deploying
.\deploy-certificates-component.ps1 -DryRun
```

### Step 3: Add Component to Your Recipe

Edit your ImageBuilder recipe YAML file (or use `example-recipe-with-certificates.yml`):

```yaml
components:
  # ... other components ...
  
  - name: windows-certificates-install
    parameters:
      - name: S3Bucket
        value: 'sgt-imagebuilder'
      - name: S3Prefix
        value: 'windows/certificates/'
      - name: CertificateStore
        value: 'Root'  # For root CA certificates like LawTrust
```

### Step 4: Build Your AMI

Use the AWS ImageBuilder console or CLI to build your AMI with the updated recipe.

## Adding More Certificates Later

Just upload the new certificate and rebuild the AMI:

```powershell
# Upload new certificate
.\upload-certificates-to-s3.ps1 -CertificatePath "C:\Certificates\NewCert.cer"

# Rebuild AMI (no need to redeploy the component)
```

The component will automatically install all certificates found in the S3 prefix.

## Certificate Store Options

| Store | Use For | Example |
|-------|---------|---------|
| `Root` | Root CA certificates | **LawTrust** ← Use this! |
| `CA` | Intermediate CA certificates | Internal intermediate CAs |
| `TrustedPublisher` | Code signing certificates | Software publishers |
| `AuthRoot` | Third-party root CAs | External root CAs |

**For LawTrust and similar root certificates, use `Root`.**

## Organizing Multiple Certificate Types

If you need different certificate stores:

**Option 1: Subdirectories**
```
s3://sgt-imagebuilder/windows/certificates/
  ├── root/          ← Root CA certificates
  │   └── LawTrust.cer
  └── intermediate/  ← Intermediate CA certificates
      └── InternalCA.cer
```

Recipe:
```yaml
components:
  - name: windows-certificates-install
    parameters:
      - name: S3Prefix
        value: 'windows/certificates/root/'
      - name: CertificateStore
        value: 'Root'
  
  - name: windows-certificates-install
    parameters:
      - name: S3Prefix
        value: 'windows/certificates/intermediate/'
      - name: CertificateStore
        value: 'CA'
```

**Option 2: Single directory (simpler)**
```
s3://sgt-imagebuilder/windows/certificates/
  ├── LawTrust.cer
  ├── InternalCA.cer
  └── PartnerCA.cer
```

All go to the same store (usually `Root`).

## Verification

After AMI is built, verify certificates are installed:

```powershell
# List all certificates in Root store
Get-ChildItem -Path Cert:\LocalMachine\Root

# Find LawTrust certificate
Get-ChildItem -Path Cert:\LocalMachine\Root | Where-Object { $_.Subject -like "*LawTrust*" }

# Check installation log
Get-Content C:\Temp\ServerInstalls\Logs\Certificate-Install.log
```

## Troubleshooting

### "No certificate files found"
- Check S3 path: `aws s3 ls s3://sgt-imagebuilder/windows/certificates/`
- Ensure file has `.cer` or `.crt` extension
- Verify file was uploaded successfully

### "S3 sync failed"
- Check ImageBuilder instance profile has S3 read permissions
- Verify bucket name and region are correct

### "Could not read certificate"
- File is not in DER format - convert from Base64/PEM
- File is corrupted - re-download or re-export

### Certificate not trusted after installation
- Verify you're using the correct store (`Root` for root CAs)
- Check certificate chain is complete

## Files Created

This setup creates the following files:

```
AWS-EC2Deployment/ImageBuilder/
├── components/
│   └── windows-certificates-install.yml          ← ImageBuilder component
├── deploy-certificates-component.ps1              ← Deploy component to AWS
├── upload-certificates-to-s3.ps1                  ← Upload certificates to S3
├── example-recipe-with-certificates.yml           ← Example recipe
├── CERTIFICATE-INSTALLATION-GUIDE.md              ← Detailed guide
└── README-CERTIFICATES.md                         ← This file
```

## Complete Example

```powershell
# 1. Convert certificate if needed (skip if already DER)
$base64Cert = Get-Content -Path "LawTrust.pem" -Raw
$base64Cert = $base64Cert -replace "-----BEGIN CERTIFICATE-----", ""
$base64Cert = $base64Cert -replace "-----END CERTIFICATE-----", ""
$base64Cert = $base64Cert -replace "`n", "" -replace "`r", ""
$certBytes = [Convert]::FromBase64String($base64Cert)
[System.IO.File]::WriteAllBytes("LawTrust.cer", $certBytes)

# 2. Upload to S3
.\upload-certificates-to-s3.ps1 -CertificatePath "LawTrust.cer"

# 3. Deploy component (first time only)
.\deploy-certificates-component.ps1

# 4. Use example-recipe-with-certificates.yml or add to your existing recipe

# 5. Build AMI using ImageBuilder console or CLI
```

## Need More Help?

See the detailed guide: [CERTIFICATE-INSTALLATION-GUIDE.md](CERTIFICATE-INSTALLATION-GUIDE.md)

## Summary

✅ **Use DER format** (`.cer` or `.crt`)  
✅ **Upload with validation** using `upload-certificates-to-s3.ps1`  
✅ **Deploy component once** using `deploy-certificates-component.ps1`  
✅ **Add to recipe** with `CertificateStore: 'Root'` for root CAs  
✅ **Rebuild AMI** whenever you add new certificates  

