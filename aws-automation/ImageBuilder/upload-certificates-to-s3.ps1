# Upload Certificates to S3 for ImageBuilder
# Author: <PERSON><PERSON>
# Date: 2025-01-08
# Description: Helper script to upload certificate files to S3 for ImageBuilder component

param(
    [Parameter(Mandatory=$true)]
    [string]$CertificatePath,
    
    [Parameter(Mandatory=$false)]
    [string]$S3Bucket = "sgt-imagebuilder",
    
    [Parameter(Mandatory=$false)]
    [string]$S3Prefix = "windows/certificates/",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$Validate = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false
)

# Function to write log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARNING" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        default { Write-Host $logEntry }
    }
}

# Function to validate certificate file
function Test-CertificateFile {
    param([string]$FilePath)
    
    Write-Log "Validating certificate file: $FilePath"
    
    # Check file exists
    if (!(Test-Path $FilePath)) {
        Write-Log "File not found: $FilePath" "ERROR"
        return $false
    }
    
    # Check file extension
    $extension = [System.IO.Path]::GetExtension($FilePath).ToLower()
    if ($extension -notin @('.cer', '.crt', '.der')) {
        Write-Log "Invalid file extension: $extension (expected .cer, .crt, or .der)" "WARNING"
        Write-Log "File will be uploaded but may not be processed by the component" "WARNING"
    }
    
    # Check file size
    $fileSize = (Get-Item $FilePath).Length
    if ($fileSize -eq 0) {
        Write-Log "File is empty" "ERROR"
        return $false
    }
    
    if ($fileSize -lt 100) {
        Write-Log "File is suspiciously small ($fileSize bytes)" "WARNING"
    }
    
    Write-Log "File size: $([math]::Round($fileSize / 1KB, 2)) KB"
    
    # Try to read certificate details
    try {
        $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
        $cert.Import($FilePath)
        
        Write-Log "Certificate Details:" "SUCCESS"
        Write-Log "  Subject: $($cert.Subject)"
        Write-Log "  Issuer: $($cert.Issuer)"
        Write-Log "  Thumbprint: $($cert.Thumbprint)"
        Write-Log "  Valid From: $($cert.NotBefore)"
        Write-Log "  Valid To: $($cert.NotAfter)"
        
        # Check if certificate is expired
        $now = Get-Date
        if ($cert.NotAfter -lt $now) {
            Write-Log "WARNING: Certificate has expired!" "WARNING"
            Write-Log "Expired on: $($cert.NotAfter)" "WARNING"
        } elseif ($cert.NotAfter -lt $now.AddDays(30)) {
            Write-Log "WARNING: Certificate expires soon!" "WARNING"
            Write-Log "Expires on: $($cert.NotAfter)" "WARNING"
        }
        
        if ($cert.NotBefore -gt $now) {
            Write-Log "WARNING: Certificate is not yet valid!" "WARNING"
            Write-Log "Valid from: $($cert.NotBefore)" "WARNING"
        }
        
        return $true
    } catch {
        Write-Log "Could not read certificate: $($_.Exception.Message)" "ERROR"
        Write-Log "This may not be a valid DER-format certificate" "ERROR"
        return $false
    }
}

# Function to upload file to S3
function Upload-CertificateToS3 {
    param(
        [string]$FilePath,
        [string]$Bucket,
        [string]$Prefix,
        [string]$Region
    )
    
    $fileName = [System.IO.Path]::GetFileName($FilePath)
    $s3Key = "$Prefix$fileName"
    $s3Uri = "s3://$Bucket/$s3Key"
    
    Write-Log "Uploading to S3..."
    Write-Log "  Source: $FilePath"
    Write-Log "  Destination: $s3Uri"
    Write-Log "  Region: $Region"
    
    if ($DryRun) {
        Write-Log "DRY RUN: Would upload to $s3Uri" "WARNING"
        return $true
    }
    
    try {
        $result = aws s3 cp "$FilePath" "$s3Uri" --region $Region 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Upload successful!" "SUCCESS"
            Write-Log "S3 URI: $s3Uri" "SUCCESS"
            return $true
        } else {
            Write-Log "Upload failed: $result" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Error uploading to S3: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to list S3 contents
function Show-S3Certificates {
    param(
        [string]$Bucket,
        [string]$Prefix,
        [string]$Region
    )
    
    Write-Log "Listing certificates in S3..."
    Write-Log "Location: s3://$Bucket/$Prefix"
    
    try {
        $result = aws s3 ls "s3://$Bucket/$Prefix" --region $Region 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            if ($result) {
                Write-Log "Certificates in S3:" "SUCCESS"
                $result | ForEach-Object { Write-Host "  $_" }
            } else {
                Write-Log "No certificates found in S3 (location is empty)" "WARNING"
            }
        } else {
            Write-Log "Could not list S3 contents: $result" "WARNING"
        }
    } catch {
        Write-Log "Error listing S3 contents: $($_.Exception.Message)" "WARNING"
    }
}

# Main execution
Write-Log "=== Certificate Upload to S3 ===" "INFO"
Write-Log "Certificate Path: $CertificatePath"
Write-Log "S3 Bucket: $S3Bucket"
Write-Log "S3 Prefix: $S3Prefix"
Write-Log "Region: $Region"

if ($DryRun) {
    Write-Log "DRY RUN MODE - No files will be uploaded" "WARNING"
}

try {
    # Check if path is a file or directory
    if (Test-Path $CertificatePath -PathType Container) {
        # Directory - upload all certificate files
        Write-Log "Processing directory: $CertificatePath"
        
        $certFiles = Get-ChildItem -Path $CertificatePath -Include "*.cer","*.crt","*.der" -File -Recurse
        
        if ($certFiles.Count -eq 0) {
            Write-Log "No certificate files found in directory" "ERROR"
            exit 1
        }
        
        Write-Log "Found $($certFiles.Count) certificate file(s)"
        
        $successCount = 0
        $failCount = 0
        
        foreach ($certFile in $certFiles) {
            Write-Log ""
            Write-Log "Processing: $($certFile.Name)" "INFO"
            
            # Validate if requested
            if ($Validate) {
                if (!(Test-CertificateFile -FilePath $certFile.FullName)) {
                    Write-Log "Validation failed for $($certFile.Name)" "ERROR"
                    $failCount++
                    continue
                }
            }
            
            # Upload to S3
            if (Upload-CertificateToS3 -FilePath $certFile.FullName -Bucket $S3Bucket -Prefix $S3Prefix -Region $Region) {
                $successCount++
            } else {
                $failCount++
            }
        }
        
        Write-Log ""
        Write-Log "=== Upload Summary ===" "INFO"
        Write-Log "Total files: $($certFiles.Count)"
        Write-Log "Successful: $successCount"
        Write-Log "Failed: $failCount"
        
        if ($failCount -gt 0) {
            Write-Log "Some uploads failed!" "ERROR"
            exit 1
        }
        
    } else {
        # Single file
        Write-Log "Processing single file: $CertificatePath"
        
        # Validate if requested
        if ($Validate) {
            if (!(Test-CertificateFile -FilePath $CertificatePath)) {
                Write-Log "Validation failed" "ERROR"
                exit 1
            }
        }
        
        # Upload to S3
        if (!(Upload-CertificateToS3 -FilePath $CertificatePath -Bucket $S3Bucket -Prefix $S3Prefix -Region $Region)) {
            Write-Log "Upload failed" "ERROR"
            exit 1
        }
    }
    
    # Show current S3 contents
    Write-Log ""
    Show-S3Certificates -Bucket $S3Bucket -Prefix $S3Prefix -Region $Region
    
    Write-Log ""
    Write-Log "=== Next Steps ===" "INFO"
    Write-Log "1. Deploy the certificate installation component:" "INFO"
    Write-Log "   .\deploy-certificates-component.ps1" "INFO"
    Write-Log ""
    Write-Log "2. Add the component to your ImageBuilder recipe:" "INFO"
    Write-Log "   - name: windows-certificates-install" "INFO"
    Write-Log "     parameters:" "INFO"
    Write-Log "       - name: S3Bucket" "INFO"
    Write-Log "         value: '$S3Bucket'" "INFO"
    Write-Log "       - name: S3Prefix" "INFO"
    Write-Log "         value: '$S3Prefix'" "INFO"
    Write-Log "       - name: CertificateStore" "INFO"
    Write-Log "         value: 'Root'" "INFO"
    Write-Log ""
    Write-Log "Upload completed successfully!" "SUCCESS"
    
} catch {
    Write-Log "Unexpected error: $($_.Exception.Message)" "ERROR"
    exit 1
}

