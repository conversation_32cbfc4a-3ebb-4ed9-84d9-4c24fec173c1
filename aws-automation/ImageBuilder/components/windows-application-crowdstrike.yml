# AWS ImageBuilder Component: Install CrowdStrike Falcon Sensor
# Author: <PERSON><PERSON>
# Date: 2025-01-08
# Description: Install and configure CrowdStrike Falcon Sensor for endpoint protection
# Documentation: https://falcon.crowdstrike.com/documentation/

name: windows-application-crowdstrike
description: Install CrowdStrike Falcon Sensor with customer-specific configuration
schemaVersion: 1.0

parameters:
  - SecretsManagerSecretName:
      type: string
      default: "/imagebuilder/crowdstrike/credentials"
      description: Secrets Manager secret name or ARN containing CrowdStrike CID
  - Region:
      type: string
      default: "af-south-1"
      description: AWS Region where the secret is stored

phases:
  - name: build
    steps:
      - name: CheckExistingInstallation
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
          commands:
            - |
              $svc = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
              if ($svc -and (Test-Path "C:\Program Files\CrowdStrike")) {
                  Write-Host "CrowdStrike already installed. Skipping."
                  exit 0
              }
              Write-Host "Proceeding with installation..."

      - name: VerifyInstallerExists
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $installer = "C:\Temp\ServerInstalls\Applications\crowdstrike\WindowsSensor.LionLanner.exe"
              if (!(Test-Path $installer)) {
                  Write-Error "Installer not found: $installer"
                  exit 1
              }
              if ((Get-Item $installer).Length -lt 1MB) {
                  Write-Error "Installer file invalid"
                  exit 1
              }
              Write-Host "Installer verified: $([math]::Round((Get-Item $installer).Length/1MB,2)) MB"

      - name: InstallCrowdStrikeSensor
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              # Retrieve CID from Secrets Manager
              Write-Host "Retrieving CrowdStrike credentials..."
              $secret = Get-SECSecretValue -SecretId "{{ SecretsManagerSecretName }}" -Region "{{ Region }}"
              $json = $secret.SecretString | ConvertFrom-Json
              if (!$json.CID) {
                  Write-Error "CID not found in secret"
                  exit 1
              }
              $cid = $json.CID
              Write-Host "CID retrieved successfully"

              # Install CrowdStrike Sensor
              $installer = "C:\Temp\ServerInstalls\Applications\crowdstrike\WindowsSensor.LionLanner.exe"
              $log = "C:\Temp\ServerInstalls\Logs\CrowdStrike-Install.log"
              New-Item -ItemType Directory -Path (Split-Path $log) -Force | Out-Null

              Write-Host "Installing CrowdStrike Falcon Sensor (NO_START mode for template)..."
              $p = Start-Process -FilePath $installer -ArgumentList "/install","/quiet","/norestart","NO_START=1","CID=$cid" -Wait -PassThru -NoNewWindow -RedirectStandardOutput $log

              if ($p.ExitCode -in @(0,3010)) {
                  Write-Host "Installation successful (exit code: $($p.ExitCode))"
                  Write-Host "Note: Service will not start automatically (NO_START=1)"
              } else {
                  Write-Error "Installation failed (exit code: $($p.ExitCode))"
                  if (Test-Path $log) { Get-Content $log }
                  exit 1
              }

      - name: VerifyServiceInstalled
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Start-Sleep -Seconds 5
              $svc = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
              if (!$svc) { Write-Error "Service not found"; exit 1 }

              Write-Host "Service installed successfully"
              Write-Host "Service: $($svc.Status) ($($svc.StartType))"
              Write-Host "Note: Service is configured with NO_START=1 for template use"

      - name: VerifyInstallationFiles
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $path = "C:\Program Files\CrowdStrike"
              if (!(Test-Path $path)) { Write-Error "Install dir not found"; exit 1 }

              $exe = Join-Path $path "CSFalconService.exe"
              if (Test-Path $exe) {
                  $ver = (Get-Item $exe).VersionInfo.FileVersion
                  Write-Host "CrowdStrike installed: $ver"
              } else {
                  Write-Warning "CSFalconService.exe not found"
              }

  - name: validate
    steps:
      - name: ValidateCrowdStrikeSensor
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $svc = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
              if (!$svc) { Write-Error "Service not found"; exit 1 }

              $exe = "C:\Program Files\CrowdStrike\CSFalconService.exe"
              if (!(Test-Path $exe)) { Write-Error "Executable not found"; exit 1 }

              $ver = (Get-Item $exe).VersionInfo.FileVersion
              Write-Host "=== VALIDATION SUCCESS ==="
              Write-Host "CrowdStrike Sensor: $ver"
              Write-Host "Service: $($svc.Status) ($($svc.StartType))"
              Write-Host "NO_START mode: Service will start on first boot of deployed instance"
              Write-Host "=========================="
