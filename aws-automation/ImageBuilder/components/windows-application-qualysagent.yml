# AWS ImageBuilder Component: Install Qualys Cloud Agent
# Author: <PERSON><PERSON>
# Date: 2025-010-06
# Description: Install and configure Qualys Cloud Agent for vulnerability scanning and compliance monitoring
# Documentation: https://docs.qualys.com/en/integration/aws-ec2-image-builder/ca_component/create_custom_component.htm

name: windows-application-qualysagent
description: Install Qualys Cloud Agent with customer-specific configuration
schemaVersion: 1.0

parameters:
  - SecretsManagerSecretName:
      type: string
      default: "/imagebuilder/qualys/credentials"
      description: Secrets Manager secret name or ARN containing Qualys credentials
  - Region:
      type: string
      default: "af-south-1"
      description: AWS Region where the secret is stored

phases:
  - name: build
    steps:
      - name: CheckExistingInstallation
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
          commands:
            - |
              $svc = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
              if ($svc -and (Test-Path "C:\Program Files\Qualys\QualysAgent")) {
                  Write-Host "Qualys already installed. Skipping."
                  exit 0
              }
              Write-Host "Proceeding with installation..."

      - name: VerifyInstallerExists
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $installer = "C:\Temp\ServerInstalls\Applications\qualys\QualysCloudAgent_x64.msi"
              if (!(Test-Path $installer)) {
                  Write-Error "Installer not found: $installer"
                  exit 1
              }
              if ((Get-Item $installer).Length -lt 1MB) {
                  Write-Error "Installer file invalid"
                  exit 1
              }
              Write-Host "Installer verified: $([math]::Round((Get-Item $installer).Length/1MB,2)) MB"

      - name: InstallQualysAgent
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              # Retrieve Qualys credentials from Secrets Manager
              Write-Host "Retrieving Qualys credentials..."
              $secret = Get-SECSecretValue -SecretId "{{ SecretsManagerSecretName }}" -Region "{{ Region }}"
              $json = $secret.SecretString | ConvertFrom-Json
              if (!$json.CustomerId -or !$json.ActivationId -or !$json.WebServiceUri) {
                  Write-Error "Missing required fields in secret (CustomerId, ActivationId, or WebServiceUri)"
                  exit 1
              }

              $cid = $json.CustomerId
              $aid = $json.ActivationId
              $uri = $json.WebServiceUri
              $proxy = $json.ProxyServer
              $port = $json.ProxyPort
              Write-Host "Credentials retrieved successfully"

              # Install Qualys Cloud Agent
              $installer = "C:\Temp\ServerInstalls\Applications\qualys\QualysCloudAgent_x64.msi"
              $log = "C:\Temp\ServerInstalls\Logs\QualysAgent-Install.log"
              New-Item -ItemType Directory -Path (Split-Path $log) -Force | Out-Null

              $args = @("/i", "`"$installer`"", "CustomerId={$cid}", "ActivationId={$aid}", "WebServiceUri=$uri", "/qn", "/norestart", "/L*v", "`"$log`"")
              if ($proxy -and $port) {
                  $args += "ProxyServer=$proxy", "ProxyPort=$port"
                  Write-Host "Installing Qualys Cloud Agent with proxy: ${proxy}:${port}..."
              } elseif ($proxy) {
                  $args += "ProxyServer=$proxy"
                  Write-Host "Installing Qualys Cloud Agent with proxy: $proxy..."
              } else {
                  Write-Host "Installing Qualys Cloud Agent..."
              }

              $p = Start-Process msiexec.exe -ArgumentList $args -Wait -PassThru -NoNewWindow

              if ($p.ExitCode -in @(0,1641,3010)) {
                  Write-Host "Installation successful (exit code: $($p.ExitCode))"
              } else {
                  Write-Error "Installation failed (exit code: $($p.ExitCode))"
                  if (Test-Path $log) { Get-Content $log -Tail 20 }
                  exit 1
              }

      - name: VerifyServiceStatus
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Start-Sleep -Seconds 5
              $svc = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
              if (!$svc) { Write-Error "Service not found"; exit 1 }

              if ($svc.StartType -ne "Automatic") {
                  Set-Service -Name "QualysAgent" -StartupType Automatic
              }

              if ($svc.Status -ne "Running") {
                  Start-Service -Name "QualysAgent"
                  Start-Sleep -Seconds 3
              }

              $svc = Get-Service -Name "QualysAgent"
              Write-Host "Service: $($svc.Status) ($($svc.StartType))"

      - name: VerifyInstallationFiles
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $path = "C:\Program Files\Qualys\QualysAgent"
              if (!(Test-Path $path)) { Write-Error "Install dir not found"; exit 1 }

              $exe = Join-Path $path "QualysAgent.exe"
              if (Test-Path $exe) {
                  $ver = (Get-Item $exe).VersionInfo.FileVersion
                  Write-Host "Qualys installed: $ver"
              } else {
                  Write-Warning "QualysAgent.exe not found"
              }

  - name: validate
    steps:
      - name: ValidateQualysAgent
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $svc = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
              if (!$svc) { Write-Error "Service not found"; exit 1 }
              if ($svc.Status -ne "Running") { Write-Error "Service not running"; exit 1 }

              $exe = "C:\Program Files\Qualys\QualysAgent\QualysAgent.exe"
              if (!(Test-Path $exe)) { Write-Error "Executable not found"; exit 1 }

              $ver = (Get-Item $exe).VersionInfo.FileVersion
              Write-Host "=== VALIDATION SUCCESS ==="
              Write-Host "Qualys Agent: $ver"
              Write-Host "Service: $($svc.Status)"
              Write-Host "=========================="
