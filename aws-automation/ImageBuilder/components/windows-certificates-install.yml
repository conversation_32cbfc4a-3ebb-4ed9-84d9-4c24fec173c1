# AWS ImageBuilder Component: Install Trusted Root Certificates
# Author: <PERSON><PERSON>
# Date: 2025-01-08
# Description: Install trusted root certificates (e.g., LawTrust) from S3 to Windows certificate stores
# Documentation: https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/certutil

name: windows-certificates-install
description: Install trusted root certificates from S3 to Windows certificate stores
schemaVersion: 1.0

parameters:
  - S3Bucket:
      type: string
      default: "sgt-imagebuilder"
      description: S3 bucket containing certificate files
  - S3Prefix:
      type: string
      default: "windows/certificates/"
      description: S3 prefix path for certificates
  - Region:
      type: string
      default: "af-south-1"
      description: AWS Region for S3 bucket
  - CertificateStore:
      type: string
      default: "Root"
      description: Certificate store location (Root, CA, TrustedPublisher, etc.)

phases:
  - name: build
    steps:
      - name: CreateCertificateDirectory
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "=== Certificate Installation Component ==="
              Write-Host "Creating certificate staging directory..."
              
              $certDir = "C:\Temp\ServerInstalls\Certificates"
              if (!(Test-Path $certDir)) {
                  New-Item -ItemType Directory -Path $certDir -Force | Out-Null
                  Write-Host "Created directory: $certDir"
              } else {
                  Write-Host "Directory already exists: $certDir"
              }

      - name: DownloadCertificatesFromS3
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Downloading certificates from S3..."
              
              $s3Bucket = "{{ S3Bucket }}"
              $s3Prefix = "{{ S3Prefix }}"
              $region = "{{ Region }}"
              $certDir = "C:\Temp\ServerInstalls\Certificates"
              
              Write-Host "Configuration:"
              Write-Host "  S3 Bucket: s3://$s3Bucket/$s3Prefix"
              Write-Host "  Target Directory: $certDir"
              Write-Host "  Region: $region"
              
              # Set AWS environment
              $env:AWS_DEFAULT_REGION = $region
              
              # Download certificates using AWS CLI
              try {
                  Write-Host "Executing: aws s3 sync s3://$s3Bucket/$s3Prefix $certDir --region $region"
                  $syncResult = aws s3 sync "s3://$s3Bucket/$s3Prefix" "$certDir" --region $region 2>&1
                  
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "S3 sync completed successfully"
                      if ($syncResult) {
                          $syncResult | ForEach-Object { Write-Host "  $_" }
                      }
                  } else {
                      Write-Error "S3 sync failed with exit code: $LASTEXITCODE"
                      Write-Error "Output: $syncResult"
                      exit 1
                  }
              } catch {
                  Write-Error "Error downloading certificates: $($_.Exception.Message)"
                  exit 1
              }
              
              # Verify certificates were downloaded
              $certFiles = Get-ChildItem -Path $certDir -Filter "*.cer" -Recurse -ErrorAction SilentlyContinue
              if (!$certFiles -or $certFiles.Count -eq 0) {
                  # Try .crt extension as well
                  $certFiles = Get-ChildItem -Path $certDir -Filter "*.crt" -Recurse -ErrorAction SilentlyContinue
              }
              
              if (!$certFiles -or $certFiles.Count -eq 0) {
                  Write-Error "No certificate files (.cer or .crt) found in $certDir"
                  exit 1
              }
              
              Write-Host "Found $($certFiles.Count) certificate file(s):"
              foreach ($cert in $certFiles) {
                  $size = [math]::Round($cert.Length / 1KB, 2)
                  Write-Host "  - $($cert.Name) ($size KB)"
              }

      - name: InstallCertificates
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Installing certificates to Windows certificate store..."
              
              $certDir = "C:\Temp\ServerInstalls\Certificates"
              $certStore = "{{ CertificateStore }}"
              $logFile = "C:\Temp\ServerInstalls\Logs\Certificate-Install.log"
              
              # Create log directory
              New-Item -ItemType Directory -Path (Split-Path $logFile) -Force | Out-Null
              
              Write-Host "Certificate Store: Cert:\LocalMachine\$certStore"
              
              # Find all certificate files
              $certFiles = @()
              $certFiles += Get-ChildItem -Path $certDir -Filter "*.cer" -Recurse -ErrorAction SilentlyContinue
              $certFiles += Get-ChildItem -Path $certDir -Filter "*.crt" -Recurse -ErrorAction SilentlyContinue
              
              if ($certFiles.Count -eq 0) {
                  Write-Error "No certificate files found"
                  exit 1
              }
              
              $installedCount = 0
              $skippedCount = 0
              $failedCount = 0
              
              foreach ($certFile in $certFiles) {
                  Write-Host ""
                  Write-Host "Processing: $($certFile.Name)"
                  
                  try {
                      # Read certificate to get details
                      $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
                      $cert.Import($certFile.FullName)
                      
                      Write-Host "  Subject: $($cert.Subject)"
                      Write-Host "  Issuer: $($cert.Issuer)"
                      Write-Host "  Thumbprint: $($cert.Thumbprint)"
                      Write-Host "  Valid From: $($cert.NotBefore)"
                      Write-Host "  Valid To: $($cert.NotAfter)"
                      
                      # Check if certificate is already installed
                      $existingCert = Get-ChildItem -Path "Cert:\LocalMachine\$certStore" -Recurse -ErrorAction SilentlyContinue | 
                          Where-Object { $_.Thumbprint -eq $cert.Thumbprint }
                      
                      if ($existingCert) {
                          Write-Host "  Status: Already installed (skipping)" -ForegroundColor Yellow
                          $skippedCount++
                          continue
                      }
                      
                      # Install certificate using Import-Certificate
                      Write-Host "  Installing to LocalMachine\$certStore..."
                      $result = Import-Certificate -FilePath $certFile.FullName -CertStoreLocation "Cert:\LocalMachine\$certStore" -ErrorAction Stop
                      
                      if ($result) {
                          Write-Host "  Status: Successfully installed" -ForegroundColor Green
                          $installedCount++
                          
                          # Log installation
                          $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Installed: $($certFile.Name) - Thumbprint: $($cert.Thumbprint)"
                          Add-Content -Path $logFile -Value $logEntry
                      } else {
                          Write-Warning "  Status: Installation returned no result"
                          $failedCount++
                      }
                      
                  } catch {
                      Write-Error "  Status: Failed - $($_.Exception.Message)"
                      $failedCount++
                      
                      # Try alternative method using certutil
                      Write-Host "  Attempting installation with certutil..."
                      try {
                          $certutilResult = certutil -addstore -f "$certStore" "$($certFile.FullName)" 2>&1
                          if ($LASTEXITCODE -eq 0) {
                              Write-Host "  Status: Successfully installed via certutil" -ForegroundColor Green
                              $installedCount++
                              $failedCount--
                          } else {
                              Write-Error "  certutil failed: $certutilResult"
                          }
                      } catch {
                          Write-Error "  certutil also failed: $($_.Exception.Message)"
                      }
                  }
              }
              
              Write-Host ""
              Write-Host "=== Installation Summary ==="
              Write-Host "Total certificates processed: $($certFiles.Count)"
              Write-Host "Successfully installed: $installedCount"
              Write-Host "Already installed (skipped): $skippedCount"
              Write-Host "Failed: $failedCount"
              Write-Host "============================"
              
              if ($failedCount -gt 0 -and $installedCount -eq 0) {
                  Write-Error "All certificate installations failed"
                  exit 1
              }
              
              if ($installedCount -eq 0 -and $skippedCount -eq 0) {
                  Write-Error "No certificates were installed"
                  exit 1
              }
              
              Write-Host "Certificate installation completed successfully"

  - name: validate
    steps:
      - name: ValidateCertificateInstallation
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "=== Validating Certificate Installation ==="
              
              $certDir = "C:\Temp\ServerInstalls\Certificates"
              $certStore = "{{ CertificateStore }}"
              
              # Get all certificate files that should have been installed
              $certFiles = @()
              $certFiles += Get-ChildItem -Path $certDir -Filter "*.cer" -Recurse -ErrorAction SilentlyContinue
              $certFiles += Get-ChildItem -Path $certDir -Filter "*.crt" -Recurse -ErrorAction SilentlyContinue
              
              if ($certFiles.Count -eq 0) {
                  Write-Error "No certificate files found for validation"
                  exit 1
              }
              
              $validatedCount = 0
              $missingCount = 0
              
              foreach ($certFile in $certFiles) {
                  try {
                      # Read certificate
                      $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
                      $cert.Import($certFile.FullName)
                      
                      # Check if installed
                      $installedCert = Get-ChildItem -Path "Cert:\LocalMachine\$certStore" -Recurse -ErrorAction SilentlyContinue | 
                          Where-Object { $_.Thumbprint -eq $cert.Thumbprint }
                      
                      if ($installedCert) {
                          Write-Host "✓ $($certFile.Name) - Thumbprint: $($cert.Thumbprint)" -ForegroundColor Green
                          $validatedCount++
                      } else {
                          Write-Warning "✗ $($certFile.Name) - NOT FOUND in certificate store"
                          $missingCount++
                      }
                  } catch {
                      Write-Warning "✗ $($certFile.Name) - Validation error: $($_.Exception.Message)"
                      $missingCount++
                  }
              }
              
              Write-Host ""
              Write-Host "=== Validation Summary ==="
              Write-Host "Certificates validated: $validatedCount / $($certFiles.Count)"
              Write-Host "Missing certificates: $missingCount"
              
              # List all certificates in the store
              Write-Host ""
              Write-Host "All certificates in LocalMachine\$certStore store:"
              $allCerts = Get-ChildItem -Path "Cert:\LocalMachine\$certStore" -ErrorAction SilentlyContinue
              if ($allCerts) {
                  foreach ($c in $allCerts) {
                      Write-Host "  - Subject: $($c.Subject)"
                      Write-Host "    Thumbprint: $($c.Thumbprint)"
                      Write-Host "    Expires: $($c.NotAfter)"
                  }
              } else {
                  Write-Host "  (No certificates found in store)"
              }
              
              Write-Host "=========================="
              
              if ($validatedCount -eq 0) {
                  Write-Error "Validation failed: No certificates were successfully validated"
                  exit 1
              }
              
              Write-Host "Validation completed successfully"

