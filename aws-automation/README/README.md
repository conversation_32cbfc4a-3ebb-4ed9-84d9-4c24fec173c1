# Windows Applications Directory

This directory contains Windows application installers and related files for AWS ImageBuilder automation.

## Directory Structure

```
applications/
├── README.md                    # This file
├── .gitkeep                     # Maintains directory in Git
├── vault/                       # HashiCorp Vault
│   ├── .gitkeep                 # Directory placeholder
│   ├── vault.exe                # Vault executable (ignored by Git)
│   └── vault-version.txt        # Version metadata (ignored by Git)
├── qualys/                      # Qualys Cloud Agent
│   ├── .gitkeep                 # Directory placeholder
│   ├── QualysCloudAgent_x64.msi # Qualys installer (ignored by Git)
│   └── install-config.json      # Installation configuration
└── [other-apps]/                # Additional applications
    ├── .gitkeep                 # Directory placeholder
    ├── installer.*              # Application installer (ignored by Git)
    └── config files              # Configuration files (tracked by Git)
```

## File Types

### Ignored by Git (.gitignore)
- **Binary Installers**: `.msi`, `.exe`, `.zip`, `.rar`, `.7z`, `.tar.gz`, `.iso`
- **Large Archives**: Any compressed files containing applications
- **Version Files**: Auto-generated version tracking files

### Tracked by Git
- **Configuration Files**: `.json`, `.xml`, `.cfg`, `.conf`, `.ini`
- **Documentation**: `README.md`, `.txt` files
- **Installation Scripts**: `.ps1`, `.bat`, `.cmd` files
- **Directory Structure**: `.gitkeep` files

## Adding New Applications

### 1. Create Application Directory
```bash
mkdir S3/windows/applications/my-app
```

### 2. Add .gitkeep File
```bash
echo "# My Application Directory" > S3/windows/applications/my-app/.gitkeep
```

### 3. Add Configuration Files
```bash
# Example configuration
cat > S3/windows/applications/my-app/install-config.json << EOF
{
    "applicationName": "My Application",
    "version": "1.0.0",
    "silentInstallArgs": "/S /SILENT",
    "requiresReboot": false
}
EOF
```

### 4. Upload Binary Files to S3
```bash
# Upload installer to S3 (not Git)
aws s3 cp my-app-installer.msi s3://sgt-imagebuilder/windows/applications/my-app/
```

## Automated Sync

### HashiCorp Vault
- **Source**: GitHub releases (automated via Lambda)
- **Sync Frequency**: Daily
- **Files**: `vault.exe`, `vault-version.txt`
- **Automation**: See `Python/lambda-vault-sync.py`

### Other Applications
- **Source**: Manual upload to S3
- **Sync Method**: `windows-applications-s3` ImageBuilder component
- **Target**: `C:\Temp\ServerInstalls\Applications\`

## Usage in ImageBuilder

### 1. Sync Applications from S3
```yaml
components:
  - name: windows-applications-s3
    parameters:
      - name: S3BucketName
        value: sgt-imagebuilder
      - name: S3Prefix
        value: windows/applications
```

### 2. Install Specific Applications
```yaml
components:
  - name: windows-install-vault
    parameters: []
  
  - name: install-qualys-agent
    parameters: []
```

## Security Considerations

### Binary File Exclusion
- **Why**: Binary files are large and can contain sensitive information
- **How**: Comprehensive .gitignore patterns
- **Alternative**: Store in S3 with proper access controls

### Configuration Files
- **Include**: Non-sensitive configuration files
- **Exclude**: Files containing passwords, tokens, or API keys
- **Best Practice**: Use parameter references for sensitive data

### Access Control
- **S3 Bucket**: Restrict access to authorized IAM roles
- **Git Repository**: Public repository should not contain binaries
- **ImageBuilder**: Use least-privilege IAM policies

## Troubleshooting

### Missing Applications
1. Check S3 bucket contents
2. Verify `windows-applications-s3` component logs
3. Ensure proper IAM permissions

### Large Repository Size
1. Check for accidentally committed binaries
2. Use `git filter-branch` to remove large files from history
3. Verify .gitignore patterns are working

### Sync Issues
1. Check Lambda function logs (for Vault)
2. Verify S3 bucket permissions
3. Test manual sync scripts

## Best Practices

### 1. Directory Organization
- One directory per application
- Consistent naming conventions
- Include version in directory name if needed

### 2. Documentation
- README.md in each application directory
- Document installation requirements
- Include configuration examples

### 3. Version Management
- Track application versions in configuration files
- Use semantic versioning where possible
- Document compatibility requirements

### 4. Testing
- Test installations in development environment
- Validate silent installation parameters
- Check for conflicts between applications

## Examples

### Chrome Browser
```
applications/chrome/
├── .gitkeep
├── README.md
├── install-config.json
└── chrome-installer.exe (in S3 only)
```

### Office 365
```
applications/office365/
├── .gitkeep
├── README.md
├── configuration.xml
├── install-config.json
└── setup.exe (in S3 only)
```

### SQL Server Management Studio
```
applications/ssms/
├── .gitkeep
├── README.md
├── install-config.json
└── SSMS-Setup-ENU.exe (in S3 only)
```
