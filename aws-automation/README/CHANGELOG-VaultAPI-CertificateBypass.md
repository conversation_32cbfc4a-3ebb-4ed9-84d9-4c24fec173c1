# Certificate Validation Bypass for Vault API - PowerShell Version Compatibility

## Date: 2025-10-08

## Summary
Updated Vault API scripts to support certificate validation bypass across all PowerShell versions (5.1 through 7.4+).

## Problem
The `-SkipCertificateCheck` parameter on `Invoke-RestMethod` only works in PowerShell Core 6+ and causes errors in Windows PowerShell 5.1, which is still commonly used on Windows Server instances.

## Solution
Implemented version-aware certificate validation bypass that works with both:
- **Windows PowerShell 5.1 and earlier**: Uses `ServicePointManager` with custom `TrustAllCertsPolicy` class
- **PowerShell Core 6+ (including 7.4)**: Uses the `-SkipCertificateCheck` parameter

## Files Modified

### 1. `AWS-EC2Deployment/SystemsManager/commands/SSM-Command-JoinDomainWithVaultApi.yaml`
- Added PowerShell version detection at script start
- Configured certificate bypass for PS 5.1 using ServicePointManager
- Added conditional logic for both Vault API calls (authentication and credential retrieval)

### 2. `AWS-EC2Deployment/SystemsManager/commands/JoinDomainWithVaultAPI.ps1`
- Same changes as YAML file for standalone script testing

## Technical Details

### PowerShell 5.1 Approach
```powershell
# Setup certificate validation bypass
if ($PSVersionTable.PSVersion.Major -le 5) {
    add-type @"
using System.Net;
using System.Security.Cryptography.X509Certificates;
public class TrustAllCertsPolicy : ICertificatePolicy {
    public bool CheckValidationResult(
        ServicePoint srvPoint, X509Certificate certificate,
        WebRequest request, int certificateProblem) {
        return true;
    }
}
"@
    [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
}
```

### PowerShell 7.4+ Approach
```powershell
# Use -SkipCertificateCheck parameter
$response = Invoke-RestMethod -Uri 'https://vault.aws.sanlamcloud.co.za/...' -SkipCertificateCheck
```

## Affected Vault Endpoints
- `https://vault.aws.sanlamcloud.co.za/v1/auth/aws-afs1/login`
- `https://vault.aws.sanlamcloud.co.za/v1/kv/data/MUD_AD/EC2Automation/PRD/EC2DomainJoin`

## Testing Recommendations
1. Test on Windows Server 2019/2022 with PowerShell 5.1
2. Test on systems with PowerShell 7.4 installed
3. Verify both authentication and credential retrieval work correctly
4. Confirm domain join process completes successfully

## References
- Pattern based on existing implementation in: `autodeploy\iac-automation\scripts\pre-deploy\connectivity-check.ps1`
- Microsoft Docs: [Invoke-RestMethod -SkipCertificateCheck](https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.utility/invoke-restmethod)

## Notes
- The ServicePointManager approach applies globally to the PowerShell session
- The -SkipCertificateCheck approach is per-request and more secure
- Both methods achieve the same result for the vault.aws.sanlamcloud.co.za endpoints

