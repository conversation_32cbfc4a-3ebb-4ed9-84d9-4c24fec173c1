# Setup certificate validation bypass for vault.aws.sanlamcloud.co.za
if ($PSVersionTable.PSVersion.Major -le 5) {
    # Windows PowerShell 5.1 and earlier - use ServicePointManager
    Write-Output "Configuring certificate validation bypass for Windows PowerShell..."
    add-type @"
using System.Net;
using System.Security.Cryptography.X509Certificates;
public class TrustAllCertsPolicy : ICertificatePolicy {
    public bool CheckValidationResult(
        ServicePoint srvPoint, X509Certificate certificate,
        WebRequest request, int certificateProblem) {
        return true;
    }
}
"@
    [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
}

# Retrieve IMDS token
$imdsToken = Invoke-RestMethod -Method Put -Uri 'http://169.254.169.254/latest/api/token' -Headers @{'X-aws-ec2-metadata-token-ttl-seconds' = '61'}

# Retrieve IAM role name (not ARN)
$iamRoleRaw = Invoke-RestMethod -Uri 'http://169.254.169.254/latest/meta-data/iam/security-credentials/' -Headers @{'X-aws-ec2-metadata-token' = $imdsToken}

# Extract just the role name if it's an ARN (format: arn:aws:iam::account:role/role-name)
if ($iamRoleRaw -match 'arn:aws:iam::[0-9]+:role/(.+)') {
    $iamRole = $matches[1]
    Write-Output "Extracted role name from ARN: $iamRole"
} else {
    $iamRole = $iamRoleRaw
    Write-Output "IAM Role name: $iamRole"
}

# Retrieve PKCS7 and remove newlines
$pkcs7 = (Invoke-RestMethod -Uri 'http://169.254.169.254/latest/dynamic/instance-identity/pkcs7' -Headers @{'X-aws-ec2-metadata-token' = $imdsToken}).Replace("`n", "").Replace("`r", "")

# Create JSON payload
$payload = @{
    role   = $iamRole
    pkcs7  = $pkcs7
} | ConvertTo-Json

# Authenticate with Vault to get client token
if ($PSVersionTable.PSVersion.Major -le 5) {
    # Windows PowerShell 5.1 - certificate bypass already configured via ServicePointManager
    $response = Invoke-RestMethod -Method Post -Uri 'https://vault.aws.sanlamcloud.co.za/v1/auth/aws-afs1/login' -ContentType 'application/json' -Body $payload
} else {
    # PowerShell Core 6+ - use -SkipCertificateCheck parameter
    $response = Invoke-RestMethod -Method Post -Uri 'https://vault.aws.sanlamcloud.co.za/v1/auth/aws-afs1/login' -ContentType 'application/json' -Body $payload -SkipCertificateCheck
}
$vaultToken = $response.auth.client_token

# Make the Vault API call
if ($PSVersionTable.PSVersion.Major -le 5) {
    # Windows PowerShell 5.1 - certificate bypass already configured via ServicePointManager
    $response = Invoke-RestMethod -Method Get -Uri 'https://vault.aws.sanlamcloud.co.za/v1/kv/data/MUD_AD/EC2Automation/PRD/EC2DomainJoin' -Headers @{'X-Vault-Token' = $vaultToken} -Verbose
} else {
    # PowerShell Core 6+ - use -SkipCertificateCheck parameter
    $response = Invoke-RestMethod -Method Get -Uri 'https://vault.aws.sanlamcloud.co.za/v1/kv/data/MUD_AD/EC2Automation/PRD/EC2DomainJoin' -Headers @{'X-Vault-Token' = $vaultToken} -SkipCertificateCheck -Verbose
}

# Extract the key-value pair from data.data
$data = $response.data.data
if ($data) {
    # Get the first key-value pair (assuming single pair as per response)
    $userid = ($data.PSObject.Properties | Select-Object -First 1).Name
    $password = $data.$userid

    # Output the extracted values
    Write-Output "UserID: $userid"
    Write-Output "Password: $password"
} else {
    Write-Error "No data found in response.data.data"
}